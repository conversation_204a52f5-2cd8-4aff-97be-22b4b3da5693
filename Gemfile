# frozen_string_literal: true

source 'https://rubygems.org'
git_source(:above_lending) { |repo| "https://github.com/Above-Lending/#{repo}.git" }

ruby '3.3.7'

gem 'rails', '~> 7.2.2'

gem 'sprockets-rails' # can be removed when pghero is removed
gem 'vite_plugin_legacy'
gem 'vite_rails'

gem 'stimulus-rails'
gem 'turbo-rails'
gem 'view_component'

gem 'pg', '~> 1.6'
gem 'pghero'
gem 'puma', '~> 6.6'
gem 'rails-pg-extras'
gem 'redis', '~> 5.4'
gem 'redis-clustering', '~> 5.0'
gem 'scenic', '~> 1.9'

gem 'bootsnap', require: false
gem 'tzinfo-data', platforms: %i[mingw mswin x64_mingw jruby]

gem 'aws-sdk-s3', '~> 1.195'

gem 'async', '~> 2.27'
gem 'datadog', require: 'datadog/auto_instrument'
gem 'deepsort', '~> 0.5'
gem 'dogstatsd-ruby'
gem 'ejs', '~> 1.1'
gem 'execjs', '~> 2.10.0'
gem 'logstop'
gem 'rack-cors'
gem 'rack-sanitizer' # can be removed once the this rails issue is resolved: https://github.com/rails/rails/issues/52114
gem 'sinatra', require: false
gem 'slack-ruby-client'
gem 'smarter_csv', '~> 1.14'

gem 'sidekiq'
gem 'sidekiq-cron'
gem 'sidekiq-unique-jobs'
source 'https://gems.contribsys.com/' do
  gem 'sidekiq-pro'
end

gem 'rswag-api'
gem 'rswag-ui'

gem 'blueprinter', '~> 1.1.2'
gem 'flipper'
gem 'flipper-notifications'
gem 'flipper-redis'
gem 'flipper-ui'
gem 'hexapdf'
gem 'liquid', '~> 5.8.7'
gem 'wicked_pdf', '~> 2.8'
gem 'wkhtmltopdf-binary'

gem 'date_validator', '~> 0.12.0'
gem 'devise', '~> 4.8'
gem 'docusign_esign', '~> 5.3'
gem 'faraday', '~> 2.13'
gem 'faraday-multipart'
gem 'faraday-retry'
gem 'holidays', '~> 8.8'
gem 'rubyzip', require: 'zip'
gem 'valid_email2', '~> 7.0'

gem 'amazing_print'
gem 'nokogiri'
gem 'rails_semantic_logger'
gem 'seed_migration'
gem 'sendgrid-ruby'

group :development, :test, :staging, :sandbox do
  gem 'mockit', git: 'https://github.com/ineverov/mockit'
end

group :development, :test do
  gem 'brakeman'
  gem 'bundler-audit', '~> 0.9.2'
  gem 'debug', platforms: %i[mri mingw x64_mingw]
  gem 'dotenv-rails'
  gem 'erb_lint', require: false
  gem 'factory_bot_rails'
  gem 'faker'
  gem 'git'
  gem 'guard'
  gem 'guard-rspec'
  gem 'memory_profiler'
  gem 'pry-byebug'
  gem 'pry-rails'
  gem 'pry-remote'
  gem 'rails-erd'
  gem 'rswag-specs'
  gem 'rubocop'
  gem 'shoulda-matchers'
end

group :development do
  gem 'annotate'
  gem 'web-console'
end

group :test do
  gem 'axe-matchers'
  gem 'capybara'
  gem 'parallel_tests'
  gem 'rails-controller-testing'
  gem 'rspec_junit_formatter'
  gem 'rspec-rails'
  gem 'rspec-sidekiq'
  gem 'simplecov', require: false
  gem 'webmock'
end
