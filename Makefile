.DEFAULT_GOAL := help
SERVICE ?= alp

alp.setup: # setup ALP platform
	.alp/bin/setup

alp.reset: # Resets database and precompiles assets
	docker compose run --rm alp command .alp/bin/ams_setup

alp.secrets.pull: # Fetches Environment files from Bitwarden
	.alp/bin/pull_secrets

alp.secrets.push: # Pushes Environment files to Bitwarden
	.alp/bin/push_secrets

alp.start:
	@echo **Deprecating** use make start
	make start

alp.sidekiq:
	@echo **Deprecating** use make sidekiq
	make sidekiq

alp.stop:
	@echo **Deprecating** use make stop
	make stop

alp.exec.bash:
	@echo **Deprecating** use make exec.bash
	make exec.bash

alp.bash:
	@echo **Deprecating** use make bash
	SERVICE=$(SERVICE) make alp.bash

alp.migrate:
	@echo **Deprecating** use make migrate
	SERVICE=$(SERVICE) make migrate

env.development: # Sets environment to development - [local services and sandbox APIs]
	@bash -i -c "source bin/setenv development; exec bash -i"

env.sandbox: # Sets environment to sandbox
	@bash -i -c "source bin/setenv sandbox; exec bash -i"

env.staging: # Sets environment to staging
	@bash -i -c "source bin/setenv staging; exec bash -i"

start: # Start all ALP Services
	docker compose stop
	docker compose up -d
	docker attach application_management_system-alp-1

sidekiq: # Like make start, but attach to sidekiq instead of rails app
	docker compose stop sidekiq
	docker compose up -d sidekiq
	docker attach application_management_system-sidekiq-1

stop: # Stop all ALP Services
	docker compose stop

exec.bash: # Bash prompt on running the application container
	docker compose exec $(SERVICE) bash

bash: # Opens container with a Bash prompt
	docker compose run --rm $(SERVICE) command bash

migrate: # Run database and seed migrations
	docker compose run --rm $(SERVICE) command bin/rails db:migrate seed:migrate

console: # Runs Rails console 
	docker compose run --rm $(SERVICE) command bin/rails c

lint: # Rubocop linting for all changed files
	docker compose run --rm $(SERVICE) command bash -c "(git diff --name-only --diff-filter=d; git ls-files --others --exclude-standard) | grep '\.rb$ ' | xargs bin/rubocop"

lint.fix: # Rubocop linting for all changed files
	docker compose run --rm $(SERVICE) command bash -c "(git diff --name-only --diff-filter=d; git ls-files --others --exclude-standard) | grep '\.rb$ ' | xargs bin/rubocop -a"

lint.fix.all: # Rubocop linting for all files
	docker compose run --rm $(SERVICE) command bin/rubocop -a

gems: # Install gems
	docker compose run --rm $(SERVICE) command bundle install

test.all: # Run the full rspec test suite
	docker compose run --rm $(SERVICE) command bin/rspec

test.changed: # Run the rspec test for all changed spec files
	docker compose run --rm $(SERVICE) command bash -c "git diff --name-only --diff-filter=d HEAD | grep -E '^spec/.+\.rb$$' | xargs bin/rspec"

test.coverage: # Open the code coverage webpage
	open coverage/index.html

docker.ps: # Show running processes
	docker compose ps

docker.restart.alp: # Restart the alp container
	docker compose restart

docker.prune: # Prune and free up system file space
	docker system prune --all --force

docker.prune.all: # Prune All (including volumes) and free up system file space
	docker system prune --all --force --volumes

vite.watch: # Compiles and watches vite assets
	bin/vite dev

# Show help topics
help:
	@grep -E '^[a-zA-Z0-9_.-]+:.*?# .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?# "}; {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}'
