#!/usr/bin/env bash

export BASH_SILENCE_DEPRECATION_WARNING=1

# Checks if called by source, so we can ensure environment variables are set
# properly
is_sourced() {
  [ "${BASH_SOURCE[0]}" != "$0" ]
}

! is_sourced && {
  echo "Sets the environment for Docker Compose."
  echo "  Usage: source bin/setenv [environment]"
  exit 1
}

# Assign Environment
if (( $# > 0 )); then
  data_environment=$1;
else
  data_environment="development"
fi

dot_env_file=".alp/.env.${data_environment}"
compose_files="compose.${data_environment}.yml"

# Checks if environment file exists or raises an error
if [ ! -f $dot_env_file ]; then
echo "ERROR: ${dot_env_file} is not found, please add it from BitWarden"
echo -e "run 'make get_environment_files'\n"
echo -e "\tsetenv usage:\n"
echo -e "\tbin/setenv environment"
echo -e "\tex: bin/setenv development"
echo -e "\tex: bin/setenv sandbox"

return 1
fi

export COMPOSE_FILE="compose.yml:${compose_files}"
export PS1="AMS ALP [SETENV: ${data_environment}] $ "

echo -ne "\033]0;AMS ENV ${data_environment}\007"

echo "Starting a new shell with:"
echo "  COMPOSE_FILE=$COMPOSE_FILE"
echo "  PS1=$PS1"
echo ""
echo "Type 'exit' to leave the ${data_environment} environment."
echo ""

