# frozen_string_literal: true

module Servicing
  class UpcomingPayments < Base
    attribute :payments

    def initialize(payments)
      payload = {}
      payload['payments'] = payments.to_a.map do |payment|
        Payment.new(payment)
      end
      payload['payments'].sort_by!(&:date)

      super(payload)
    end

    def any_autopay?
      payments.any? { |payment| payment.type == 'recurring' && payment.amount.positive? }
    end

    def latest_payment
      payments.max_by(&:id)
    end
  end
end
