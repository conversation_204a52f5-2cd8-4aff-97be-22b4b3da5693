# frozen_string_literal: true

module Servicing
  class PaymentProfile < Base
    attribute :id, :integer
    attribute :primary, :boolean
    attribute :secondary, :boolean
    attribute :title, :string
    attribute :type, :string
    attribute :checking_account_id, :integer
    attribute :active, :boolean
    attribute :visible, :boolean
    attribute :bank_name, :string
    attribute :account_number, :string
    attribute :routing_number, :string

    def initialize(hash)
      payment_profile = hash.dup
      payment_profile['primary'] = payment_profile.delete('isPrimary')
      payment_profile['secondary'] = payment_profile.delete('isSecondary')
      payment_profile['checking_account_id'] = payment_profile.delete('checkingAccountId')
      payment_profile['bank_name'] = payment_profile.delete('bankName').to_s
      payment_profile['account_number'] = payment_profile.delete('accountNumber')
      payment_profile['routing_number'] = payment_profile.delete('routingNumber')

      super(payment_profile)
    end

    # NOTE: When customers use Plaid to connect their Chase account, the account
    # number is tokenized. This tokenized account number eventually gets stored
    # in LoanPro, which means the last 4 digits we get from the LoanPro API
    # are not tied to the customers actual bank account, which often leads to
    # confusion and additional support calls because the number is not something
    # they recognize. This is outlined in more detail here:
    # https://abovelending.atlassian.net/browse/NILE-448?focusedCommentId=32369
    #
    # Unfortunately, displaying the real last 4 digits of a Chase account would
    # require a good amount of re-work and is not something we want to prioritize
    # at this point in time. Therefore, we're using the Bank Name (which is a
    # little hacky and prone to error) to hide the last 4 for Chase customers only.
    #
    def label
      is_chase = ['chase', 'jpmorgan chase'].include?(bank_name.downcase)
      return bank_name if is_chase

      "#{bank_name} (...#{account_number})".strip
    end
  end
end
