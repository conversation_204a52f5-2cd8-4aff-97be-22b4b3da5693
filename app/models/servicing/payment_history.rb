# frozen_string_literal: true

module Servicing
  class PaymentHistory < Base
    attribute :payments

    def initialize(payments)
      payload = {}
      payload['payments'] = payments.to_a.map do |payment|
        payment['type'] = payment_type(payment.delete('info'))
        Payment.new(payment)
      end

      super(payload)
    end

    private

    def payment_type(payment_info)
      case payment_info
      when /scheduled payment/i
        'Auto Pay'
      when /representment of payment/i
        'Representment'
      when /customer initiated payment/i
        'One-Time'
      else
        'Unknown'
      end
    end
  end
end
