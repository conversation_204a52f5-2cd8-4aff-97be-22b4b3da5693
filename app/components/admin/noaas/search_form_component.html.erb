<%= form_with(model: form_model, url: admin_noaas_search_path) do |form| %>
  <div class="space-y-8">
    <div>
      <h3 class="text-lg font-semibold text-gray-800 border-b pb-2">
        Search by Loan Identifier
      </h3>
      <div class="mt-4">
        <%= render UI::InputTextComponent.new(
              form:,
              field: :loan_identifier,
              label: 'Loan Identifier',
              placeholder: 'Enter Unified ID, Request ID or Loan ID'
            ) %>
      </div>
    </div>

    <div class="relative flex items-center justify-center text-sm text-gray-500">
      <div class="absolute inset-0 flex items-center" aria-hidden="true">
        <div class="w-full border-t border-gray-300"></div>
      </div>
      <span class="relative bg-white px-3 uppercase tracking-wide">OR</span>
    </div>

    <div>
      <h3 class="text-lg font-semibold text-gray-800 border-b pb-2">
        Search by NOAA filename attributes
      </h3>
      <p class="text-sm text-gray-500 mt-1">
        Use this to search for older UPL NOAA documents that were not linked to a loan record.
      </p>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
        <%= render UI::InputTextComponent.new(
              form:,
              field: :first_name,
              label: 'Borrower First Name',
              placeholder: 'Enter First Name'
            ) %>

        <%= render UI::InputTextComponent.new(
              form:,
              field: :last_name,
              label: 'Borrower Last Name',
              placeholder: 'Enter Last Name'
            ) %>
      </div>

      <div class="mt-4">
        <%= render UI::InputDateComponent.new(
              form:,
              field: :document_created_on,
              label: 'Document Created On (optional)'
            ) %>
      </div>
    </div>

    <div class="pt-4">
      <%= render UI::SubmitComponent.new(form:).with_content('Search') %>
    </div>
  </div>
<% end %>
