# frozen_string_literal: true

module Talkdesk
  class OrchestrateCallbackJob < ApplicationJob
    sidekiq_options queue: 'default', tags: %w[talkdesk application_dropoff]

    # NOTE: Phone applications with a credit freeze in place have the status `BASIC_INFO_COMPLETE`.
    #       These loan applications need to be added to the dropoff list to help the borrower progress.
    PHONE_DROPOFF_STATUSES = %w[BASIC_INFO_COMPLETE APPROVED].freeze
    WEB_DROPOFF_STATUSES = %w[
      OFFERED
      PENDING
      APPROVED
    ].freeze

    def perform(loan_id)
      loan = ::Loan.find(loan_id)
      remove_enqueued_calls(loan)

      return unless eligible_for_callback?(loan.source_type, loan.loan_app_status.name)

      Talkdesk::CheckApplicationDropoffJob.perform_in(
        Talkdesk::CheckApplicationDropoffJob::FOLLOWUP_TALKDESK_DELAY,
        loan_id,
        loan.loan_app_status.name
      )

      meta = {
        loan_id:,
        loan_app_status: loan.loan_app_status.name,
        delay_seconds: Talkdesk::CheckApplicationDropoffJob::FOLLOWUP_TALKDESK_DELAY.to_i
      }
      notify('orchestrate_callback_job', { success: true, meta: })
    end

    private

    def eligible_for_callback?(source_type, loan_app_status_name)
      case source_type
      when ::Loan::PHONE_SOURCE_TYPE
        PHONE_DROPOFF_STATUSES.include?(loan_app_status_name)
      when ::Loan::WEB_SOURCE_TYPE
        WEB_DROPOFF_STATUSES.include?(loan_app_status_name)
      end
    end

    def remove_enqueued_calls(loan)
      TalkdeskEvent
        .where(loan:, disposition: TalkdeskEvent::QUEUED_DISPOSITION)
        .each { |queued_callback| remove_enqueued_call(queued_callback) }
    end

    def remove_enqueued_call(queued_callback)
      meta = { talkdesk_event_id: queued_callback.id }

      talkdesk_api.delete_record_from_list_by_id(
        queued_callback.talkdesk_list_id,
        queued_callback.talkdesk_record_id,
        raise_not_found: true
      )

      queued_callback.update!(disposition: TalkdeskEvent::QUEUE_REMOVED_DISPOSITION)
      notify('orchestrate_callback_job_queued_removal', { success: true, meta: })
    rescue Clients::TalkdeskApi::ResourceNotFound
      notify('orchestrate_callback_job_queued_removal', { success: false, fail_reason: 'could not remove', meta: })

      queued_callback.update!(disposition: TalkdeskEvent::UNKNOWN_DISPOSITION)
    end

    def talkdesk_api
      return @talkdesk_api if defined?(@talkdesk_api)

      @talkdesk_api = Clients::TalkdeskApi.new
    end
  end
end
