# frozen_string_literal: true

module AutomatedVerification
  class AutomatedVerificationsJob < ApplicationJob # rubocop:disable Metrics/ClassLength
    sidekiq_options queue: 'default', tags: %w[loans todos automated_verifications], retry: 3

    EVENT_NAME = 'automated_verification_execution'

    ELIGIBLE_LOAN_APP_STATUS_IDS = [::LoanAppStatus::PENDING_STATUS,
                                    ::LoanAppStatus::READY_FOR_REVIEW_STATUS,
                                    ::LoanAppStatus::AUTO_VERIFICATION_PROCESSING_STATUS].map do |status|
      ::LoanAppStatus.id(status)
    end
    ELIGIBLE_TODO_STATUSES = [Todo.statuses[:pending]].freeze
    REQUIRED_PLAID_REPORT_TYPES = [PlaidReport::ASSETS_REPORT_TYPE].freeze
    REQUIRED_OCROLUS_REPORT_TYPES = [OcrolusReport::BOOK_FRAUD_SIGNALS_TYPE,
                                     OcrolusReport::BOOK_SUMMARY_TYPE,
                                     OcrolusReport::ENRICHED_TRANSACTIONS_TYPE].freeze

    BORROWER_REPORTS_REQUIRED_FOR_AUTO_APPROVAL = [Clients::GdsApi::Base::GIACT_REPORT_TYPE,
                                                   Clients::GdsApi::Base::SOCURE_REPORT_TYPE].freeze

    # If a Plaid loan app is found to be ineligible for automated verifications for any of these reasons, the bank
    # todo will not be transitioned to 'review' status, as we expect eligibility requirements to be met in a
    # future run of this job.
    PLAID_RECOVERY_EXPECTED_INELIGIBLE_REASONS = ['Unexpected enabled bank account count: 0',
                                                  'Missing required Plaid report(s): asset_report'].freeze

    def perform(loan_id)
      @loan_id = loan_id

      return if loan.upl?

      Gds::SyncTasks.call(loan:)

      return unless eligibility_checked?

      verification_inputs = create_verification_inputs
      verification_results = CreateVerificationResults.call(verification_inputs:)
      process_results(verification_results)
      check_for_auto_decision(verification_results)
      mark_verifications_completed
      CaseCenterQueueManager.call(loan:)
      record_event
    rescue StandardError => e
      record_event(error: e)
      raise e
    end

    private

    attr_reader :loan_id

    def eligibility_checked?
      return true if eligible?

      record_event

      # If possible, move the bank todo to manual review since it isn't eligible for automated verification
      if !expected_to_become_eligible? && eligible_loan_app_status? && bank_todo.present?
        Todos::UpdateStatus.call(todo: bank_todo, status: Todo.statuses[:review])
        CaseCenterQueueManager.call(loan:)
      end

      false
    end

    def verification_source
      @verification_source ||= determine_verification_source
    end

    # Identifies the most recently triggered source for verification reports (i.e. Plaid or Ocrolus). If the
    # application has no verification source reports, nil will be returned.
    def determine_verification_source
      newest_plaid_report = plaid_reports.max_by(&:created_at)
      newest_ocrolus_report = ocrolus_reports.max_by(&:created_at)

      if newest_plaid_report.present? &&
         (newest_ocrolus_report.blank? ||
           newest_plaid_report.created_at > newest_ocrolus_report.created_at)
        return :plaid
      end

      :ocrolus if newest_ocrolus_report.present?
    end

    def plaid_source? = verification_source == :plaid
    def ocrolus_source? = verification_source == :ocrolus
    def eligible? = ineligible_reason.blank?

    def ineligible_reason
      @ineligible_reason ||= check_for_ineligible_reason
    end

    # For a Plaid-based execution of this job, it won't be eligible on the first run if either the Plaid asset report
    # hasn't been retrieved or the borrower hasn't yet enabled a bank account. We expect it to be eligible on the next
    # attempt.
    def expected_to_become_eligible?
      # Important nuance: In the case of Plaid, verification_source is nil (and therefore plaid_source? is false) if the
      # borrower hasn't yet enabled a bank account. So instead of relying on plaid_source? here, we return false if
      # ocrolus_source? is true.
      return false if ocrolus_source?

      ineligible_reason.in?(PLAID_RECOVERY_EXPECTED_INELIGIBLE_REASONS)
    end

    def check_for_ineligible_reason
      return "Ineligible loan app status: #{loan.loan_app_status_id}" unless eligible_loan_app_status?

      return "Unexpected enabled bank account count: #{enabled_bank_accounts_count}" if enabled_bank_accounts_count != 1

      return 'No verification source reports.' if verification_source.blank?

      if missing_plaid_report_types.any?
        return "Missing required Plaid report(s): #{missing_plaid_report_types.join(', ')}"
      end

      if missing_ocrolus_report_types.any?
        return "Missing required Ocrolus report(s): #{missing_ocrolus_report_types.join(', ')}"
      end

      'No valid bank Todo found.' if bank_todo.blank?
    end

    def eligible_loan_app_status?
      ELIGIBLE_LOAN_APP_STATUS_IDS.include?(loan.loan_app_status_id)
    end

    def enabled_bank_accounts_count
      @enabled_bank_accounts_count ||= loan.bank_accounts.filter(&:enabled).count
    end

    def missing_plaid_report_types
      return [] unless plaid_source?

      @missing_plaid_report_types ||= REQUIRED_PLAID_REPORT_TYPES - plaid_reports.map(&:report_type)
    end

    def missing_ocrolus_report_types
      return [] unless ocrolus_source?

      @missing_ocrolus_report_types ||= REQUIRED_OCROLUS_REPORT_TYPES - ocrolus_reports.map(&:report_type)
    end

    def create_verification_inputs
      verification_inputs =
        if ocrolus_source?
          CreateOcrolusVerificationInputs.call(loan:, bank_account:, bank_todo:, ocrolus_reports:)
        else
          CreatePlaidVerificationInputs.call(loan:, bank_account:, bank_todo:, plaid_reports:)
        end

      event_meta[:complete_verification_inputs] = verification_inputs&.complete?
      event_meta[:credit_model_level] = verification_inputs&.credit_model_level

      verification_inputs
    end

    def process_results(verification_results)
      event_meta[:verification_rules_output] = verification_results.rules_output
      event_meta[:bank_statement_result] = verification_results.bank_statement_result

      bank_todo_status =
        if verification_results.bank_statement_result == VerificationResults::APPROVED_RESULT
          Todo.statuses[:approved]
        elsif verification_results.bank_statement_result == VerificationResults::REJECTED_RESULT
          Todo.statuses[:rejected]
        else
          Todo.statuses[:review]
        end

      Todos::UpdateStatus.call(todo: bank_todo, status: bank_todo_status)
    end

    def check_for_auto_decision(verification_results)
      # Auto approve/decline must be performed synchronously to ensure the loan's status update is actually recorded
      # before the automated verification process is completed.
      if auto_approve_loan?
        Loans::AutoApproveJob.perform_sync(loan.id)
        event_meta[:loan_auto_approved] = true
      elsif auto_decline_loan?
        # The decline reason is determined by the rule(s) that failed
        decline_reason = SelectDeclineReason.call(verification_results:)
        Loans::AutoDeclineJob.perform_sync(loan.id, decline_reason)
        event_meta[:loan_auto_declined] = true
      end
    end

    # The automated_verification_completed_at attribute cannot be set on Todo record until after the Loan has been
    # set into the approved status if it is eligible for auto-approval. Setting the automated verification completed
    # at time is the signal Lander uses to know when it can stop polling for updates to the loan.
    def mark_verifications_completed
      return unless bank_todo.automated_verification_started_at.present?

      bank_todo.update!(automated_verification_completed_at: Time.zone.now)
    end

    def auto_approve_loan?
      all_todos_approved? && valid_borrower_reports_present?
    end

    def all_todos_approved?
      event_meta[:has_approved_todos] =
        loan.todos.where(deleted_at: nil).where.not(status: Todo.statuses[:approved]).none?
    end

    def valid_borrower_reports_present?
      borrower_reports_index = Clients::GdsApi.retrieve_borrower_reports(request_id: loan.request_id)
                                              .index_by { |report| report[:type] }

      event_meta[:has_borrower_reports] = BORROWER_REPORTS_REQUIRED_FOR_AUTO_APPROVAL.all? do |required_type|
        report = borrower_reports_index[required_type]

        next false unless report.present?

        unless report[:valid]
          Rails.logger.warn("AutomatedVerificationsJob - Borrower report #{required_type} contains errors",
                            error_message: report[:error_message])
          next false
        end

        true
      end
    end

    def auto_decline_loan?
      bank_todo.rejected?
    end

    def record_event(error: nil)
      if error.blank?
        Rails.logger.info('Automated verifications completed successfully.', event_meta)
        notify_async_event(name: event_name, success: true, meta: event_meta)
      else
        Rails.logger.info('Automated verifications failed.',
                          event_meta.merge(error_message: error.message, error_class: error.class))
        notify_async_event(name: event_name,
                           success: false,
                           fail_reason: error.message,
                           meta: event_meta.merge(error_class: error.class))
      end
    end

    def loan
      @loan ||= Loan.includes(:bank_accounts).find(loan_id)
    end

    def bank_todo
      @bank_todo ||= loan.todos.where(deleted_at: nil, type: Todo.types[:bank],
                                      status: ELIGIBLE_TODO_STATUSES).order(created_at: :desc).first
    end

    def bank_account
      @bank_account ||= loan.bank_accounts.detect(&:enabled)
    end

    def plaid_reports
      @plaid_reports ||= loan.plaid_reports.where(bank_account_id: bank_account&.id).order(created_at: :desc)
    end

    def ocrolus_reports
      @ocrolus_reports ||= loan.ocrolus_reports.order(created_at: :desc)
    end

    def experiment_cohort
      @experiment_cohort ||= Experiment['2025_04_CHI_1753_Credit_Model_1_0'].fetch_cohort_for(loan.borrower)
    end

    def event_meta
      @event_meta ||= {
        loan_id:,
        verification_source:,
        ineligible_reason:,
        loan_auto_approved: false,
        loan_auto_declined: false,
        experiment_cohort:
      }
    end
  end
end
