<% content_for(:main_class) { 'border-t border-brand-gray-100 pb-20' } %>

<% content_for :sub_header do %>
  <div class="bg-white border-t border-brand-gray-100">
    <div class="max-w-(--breakpoint-2xl) mx-auto px-6 pt-10 pb-8">
      <h1 class="text-3xl">Welcome back, <%= current_borrower.first_name.titleize %></h1>
    </div>
  </div>
<% end %>

<div class="max-w-(--breakpoint-2xl) mx-auto px-6 pt-6 pb-12">
  <div class="grid grid-cols-1 lg:grid-cols-[3fr_1fr] gap-6">
    <% if @dashboard_details.debt_sale? %>
      <%= render partial: 'sold_loan',
                 locals: {
                   owner: @dashboard_details.beneficial_owner_details,
                   owner_name_fallback: @dashboard_details.beneficial_owner_name
                 } %>
    <% elsif @dashboard_details.paid_off? %>
      <%= render partial: 'closed_loan' %>
    <% else %>
      <%= render partial: 'open_loan',
                 locals: {
                   details: @dashboard_details,
                   payment_profiles: @payment_profiles,
                   upcoming_payments: @upcoming_payments,
                   payment_history: @payment_history
                 } %>
    <% end %>
  </div>
</div>
