# frozen_string_literal: true

module Servicing
  class DashboardController < BaseController
    skip_before_action :ensure_authenticated_borrower!, only: :whoops
    skip_before_action :ensure_correct_funnel_step!, only: :whoops

    def index
      initialize_dashboard

      # This session entry is used to hide the Make a Payment button when the user is
      # logged in and on the home page or other static pages, where we don't want to have
      # inline API calls to Loanpro.
      #
      session[:servicing_loan_payable] = !dashboard_details.debt_sale? && !dashboard_details.paid_off?
    rescue StandardError => e
      handle_servicing_error(e)
    ensure
      RecordRequestEvent.call(request_event_name:, request:, response:, meta: index_meta)
    end

    def whoops
    ensure
      RecordRequestEvent.call(request_event_name:, request:, response:, meta: whoops_meta)
    end

    private

    def initialize_dashboard
      dashboard_details
      payment_profiles
      payment_history
      upcoming_payments
    end

    def index_meta
      default_meta
        .merge(signed_loanpro_loan_resolver.meta)
        .merge(
          is_servicing_loan_payable: session[:servicing_loan_payable] || false
        ).compact
    end

    def dashboard_details
      @dashboard_details ||= Loanpro::DashboardDetails.call(borrower: current_borrower,
                                                            loan_id: loanpro_loan&.loanpro_loan_id)
    end

    def payment_profiles
      @payment_profiles ||= Loanpro::PaymentProfiles.call(loanpro_loan_id: loanpro_loan&.loanpro_loan_id)
    end

    def payment_history
      @payment_history ||= Loanpro::PaymentHistory.call(loan_id: loanpro_loan&.loanpro_loan_id)
    end

    def upcoming_payments
      @upcoming_payments ||= Loanpro::UpcomingPayments.call(loan_id: loanpro_loan&.loanpro_loan_id)
    end
  end
end
