# frozen_string_literal: true

module LoanApplications
  class BasicInfoController < BaseController
    PI1_WITHDRAW_ERRORS = [Pi1::ERROR_ONBOARDED_LOANS, Pi1::ERROR_APPROVED_LOANS].freeze

    before_action :initialize_basic_info_create_form_model

    skip_before_action :ensure_authenticated_borrower!

    def basic_info
      handle_missing_lead!

      session[:basic_info_viewed] = true
    ensure
      ::RecordRequestEvent.call(request_event_name:, request:, response:, meta: default_meta, form_model: @form_model)
    end

    def basic_info_create # rubocop:disable Metrics/AbcSize
      handle_missing_lead!

      return unless @form_model.valid?

      loan = pi1_service.call
      attach_spouse(loan:, form_model: @form_model)

      sign_in(loan.borrower)

      redirect_to additional_info_loan_applications_path(offer: session[:code], s: session[:service_entity])
    rescue StandardError => e
      handle_pi1_error(e)
    ensure
      render :basic_info, status: :unprocessable_entity unless performed?

      ::RecordRequestEvent.call(request_event_name:, request:, response:, meta: basic_info_create_meta,
                                form_model: @form_model)
    end

    protected

    def basic_info_create_meta
      default_meta.merge(pi1_service.meta)
    end

    private

    def handle_missing_lead!
      return unless resolver.lead_id.blank? || resolver.landing_lead_id.blank?

      # UPL loans could wind up here after expiry and will have never have any lead/landing_lead
      return if current_loan&.expired?

      session.delete(:basic_info_viewed)
      exit_to(:intake)
    end

    def initialize_basic_info_create_form_model
      @form_model = BasicInfoFormModel.new(
        **params.require(:loan_applications_basic_info_form_model)
                .permit(*BasicInfoFormModel.attribute_names)
      )
    rescue ActionController::ParameterMissing
      @form_model = BasicInfoFormModel.new
    end

    def pi1_service
      @pi1_service ||= Pi1.new(pi1_attributes(@form_model))
    end

    def pi1_attributes(form_model)
      form_model.pi1_attributes.merge(
        borrower_id: current_borrower&.id,
        client_ip: forwarded_for_ip_address(default_to_remote_ip: true),
        code: session[:code]
      )
    end

    def attach_spouse(loan:, form_model:)
      spouse_attributes = form_model.spouse_attributes&.compact_blank
      WithSpouse.call(borrower: loan.borrower, **spouse_attributes) if spouse_attributes[:married]
    end

    def handle_pi1_error(error)
      if error.is_a?(Pi1::LoanSetupError)
        if PI1_WITHDRAW_ERRORS.include?(error.message)
          return redirect_to active_application_exit_pages_path(offer: session[:code], s: session[:service_entity])
        end

        return redirect_to resume_borrowers_path if error.message == Pi1::ERROR_ACTIVE_LOAN
      end

      Rails.logger.error(message: 'PI1 submission failure.', errors: [error.message], lead_code: session[:code])

      redirect_to_whoops_path(message: error.message)
    end
  end
end
