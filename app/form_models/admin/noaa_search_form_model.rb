# frozen_string_literal: true

module Admin
  class NoaaSearchFormModel < ApplicationFormModel
    attribute :loan_identifier
    attribute :first_name
    attribute :last_name
    attribute :document_created_on

    validate :validate_search_criteria

    private

    def validate_search_criteria
      has_loan_identifier = loan_identifier.present?
      has_document_identifier = document_identifier_present?

      if has_loan_identifier && has_document_identifier
        errors.add(:base, 'Please provide either a loan identifier OR document identifier, not both.')
      elsif !has_loan_identifier && !has_document_identifier
        errors.add(:base,
                   'Please provide either a loan identifier or at least one of: first name or last name')
      end
    end

    def document_identifier_present?
      first_name.present? || last_name.present?
    end
  end
end
