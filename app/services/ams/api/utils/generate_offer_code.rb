# frozen_string_literal: true

module Ams
  module Api
    module Utils
      class GenerateOfferCode < ServiceObject
        attribute :source, :string

        LOCKED_SSN = '0000'

        def call
          call_service_object(custom_authorization: true) do
            handle_not_authorized and return if Rails.env.production?

            Lead.transaction do
              if lead
                lead.update(
                  ssn: LOCKED_SSN,
                  state: nil,
                  first_name: '<PERSON><PERSON><PERSON>',
                  last_name: 'LAMBERT',
                  email: nil,
                  program_id: 'BRP-117107'
                )
                handle_success
              else
                handle_lead_not_found
              end
            end
          end
        end

        private

        def handle_not_authorized
          @status = 403
          @body = {
            error: 'This endpoint is disabled in production'
          }
        end

        def disqualifying_loans
          valid_statuses = [13, 14, 20, 28]

          Loan
            .joins(:borrower)
            .joins('LEFT JOIN users ON users.email = borrowers.email')
            .where(product_type: 'IPL')
            .where('UPPER(loans.code) = UPPER(leads.code)')
            .where(
              Loan.arel_table[:loan_app_status_id].not_in(valid_statuses)
              .or(User.arel_table[:activated_account].eq(true))
            )
            .select('1')
        end

        def lead
          return @lead if defined?(@lead)

          # Final query: Leads with no IPL loans OR only qualifying IPL loans
          @lead = Lead
                  .lock('FOR UPDATE SKIP LOCKED')
                  .where.not(Arel.sql("EXISTS (#{disqualifying_loans.to_sql})"))
                  .where.not(ssn: LOCKED_SSN)
                  .where('expiration_date > ?', 10.minutes.since)
                  .first
        end

        def handle_success
          @status = 200
          lander_base_url = Rails.application.config_for(:general).lander_base_url
          @body = { url: "#{lander_base_url}/graduation-loan/intake-page-1?s=#{source}&offer=#{lead.code}" }
        end

        def handle_lead_not_found
          @status = 404
          @body = { error: 'No leads available to generate offer code' }
        end
      end
    end
  end
end
