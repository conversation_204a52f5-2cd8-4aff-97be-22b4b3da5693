# frozen_string_literal: true

module Documents
  class ConstructPlaidAssetReportVariables < Service::Base
    include Notifier

    class MissingPlaidReport < StandardError; end
    class MissingPlaidData < StandardError; end

    attribute :loan_id

    def call
      Rails.logger.info("#{self.class} - Constructing Plaid Asset Report.", loan_id:)
      validate!
      construct_variables
    end

    def plaid_report
      @plaid_report ||= PlaidReport.where(loan_id:, report_type: PlaidReport::ASSETS_REPORT_TYPE)
                                   .order(created_at: :desc)
                                   .first&.response
    end

    def validate!
      raise MissingPlaidReport unless plaid_report.present?

      error = plaid_report['error']
      error_code = plaid_report.dig('error', 'error_code')
      raise MissingPlaidData, error_code if error.present?
    end

    def construct_variables
      report_information.merge(asset_summary).merge(borrower_information)
                        .merge(account_holder_information).merge(account_history)
    end

    def report_information
      {
        days_requested: report_data&.dig('days_requested'),
        accounts: items_data&.dig('accounts')&.size.to_i,
        requester_user_id: user_data('client_user_id'),
        requester_report_id: plaid_report.dig('report', 'client_report_id'),
        requested_on: format_date(plaid_report.dig('report', 'date_generated')),
        data_as_of: format_date(plaid_report.dig('report', 'date_generated')),
        asset_report_id: report_data&.dig('asset_report_id')
      }
    end

    def asset_summary
      {
        institution: items_data&.dig('institution_name'),
        account_name: account&.dig('name'),
        account_mask: account&.dig('mask'),
        current_balance:,
        available_balance:,
        account_type: account&.dig('type')
      }
    end

    def account_holder_information
      owner_data = account&.dig('owners', 0)
      address_data = owner_data&.dig('addresses', 0, 'data')
      {
        name: owner_data&.dig('names', 0),
        street: address_data&.dig('street'),
        city: address_data&.dig('city'),
        state: address_data&.dig('region'),
        postal_code: address_data&.dig('postal_code'),
        country: address_data&.dig('country'),
        phone_numbers: owner_data&.dig('phone_numbers', 0, 'data'),
        emails: owner_data&.dig('emails', 0, 'data'),
        days_available: account&.dig('days_available')
      }
    end

    def borrower_information
      {
        first_name: user_data('first_name'),
        middle_name: user_data('middle_name'),
        last_name: user_data('last_name'),
        ssn: user_data('ssn'),
        phone_number: user_data('phone_number'),
        email: user_data('email')
      }
    end

    def account_history
      {
        transaction_details:,
        pending_transaction_details:
      }
    end

    private

    def account
      @account ||= plaid_report.dig('report', 'items', 0, 'accounts', 0)
    end

    def account_balances
      account&.dig('balances')
    end

    def available_balance
      account_balances&.dig('available') ? "$#{format('%.2f', account_balances['available'])}" : nil
    end

    def current_balance
      account_balances&.dig('current') ? "$#{format('%.2f', account_balances['current'])}" : nil
    end

    def user_data(key)
      plaid_report.dig('report', 'user', key)
    end

    def report_data
      plaid_report['report']
    end

    def items_data
      report_data&.dig('items', 0)
    end

    def transaction_details
      filtered_transactions(false)
    end

    def pending_transaction_details
      filtered_transactions(true)
    end

    def filtered_transactions(pending)
      return [] unless account&.dig('transactions')

      pending_transactions = account['transactions'].select { |tx| tx['pending'] == pending }
      grouped_transactions = pending_transactions.group_by { |tx| format_date(tx['date']) }
      build_all_transactions(grouped_transactions)
    end

    def build_all_transactions(grouped_transactions)
      all_transactions = []
      grouped_transactions.each do |date, transactions|
        transactions.each_with_index do |tx, index|
          transaction = build_transaction(tx, index == transactions.size - 1, date)
          all_transactions << transaction
        end
      end
      all_transactions
    end

    def build_transaction(tx_record, is_last_of_day, date)
      amount = tx_record['amount']
      transaction = {
        date: date,
        description: tx_record['original_description'],
        inflow: amount.negative? ? "$#{format('%.2f', amount.abs)}" : nil,
        outflow: amount.positive? ? "$#{format('%.2f', amount)}" : nil
      }
      add_ending_balance(transaction, date, is_last_of_day) unless tx_record['pending']
      transaction
    end

    def add_ending_balance(transaction, date, is_last_of_day)
      transaction[:ending_daily_balance] = is_last_of_day ? historical_balances[date] : nil
    end

    def historical_balances
      account&.dig('historical_balances')&.each_with_object({}) do |balance, result|
        date = format_date(balance['date'])
        current_balance = balance['current']
        result[date] = "$#{format('%.2f', current_balance)}" if date && current_balance
      end || {}
    end

    def format_date(date)
      return nil unless date.present?

      Date.parse(date).strftime('%B %d, %Y')
    end
  end
end
