# frozen_string_literal: true

module Users
  class CreateUser < Service::Base
    include Notifier

    attribute :email, :string
    attribute :first_name, :string
    attribute :last_name, :string
    attribute :password, :string
    attribute :send_email, :boolean, default: true
    attribute :service_entity_name, :string, default: User::SERVICE_ENTITY_NAMES.first

    validates :service_entity_name, allow_nil: false,
                                    inclusion: { in: User::SERVICE_ENTITY_NAMES, message: 'is invalid' }

    validate do
      user.validate
      errors.merge!(user.errors)
    end

    def call
      normalize_data!
      validate!

      # Must be done for both new and existing User records to ensure the Borrower record's identity_id is correct.
      user.borrower = Borrower.find_by(email:)
      user.save!

      if send_email
        password.present? ? send_welcome_sign_in_email : send_welcome_activation_email
      end

      user
    end

    private

    def normalize_data!
      self.email = email.downcase
      self.service_entity_name ||= User::SERVICE_ENTITY_NAMES.first
    end

    def send_welcome_sign_in_email
      Users::SendWelcomeEmail.call(email: user.email, activated_account: true)
      log_event(:welcome_and_signin_email_sent)
    end

    def send_welcome_activation_email
      Users::SendWelcomeEmail.call(email: user.email, activated_account: false)
      log_event(:welcome_email_sent)
    end

    def user
      @user ||= User.find_or_initialize_by(email:) do |u|
        u.assign_attributes(
          password:,
          first_name:,
          last_name:,
          service_entity_name:
        )
      end
    end

    def log_event(event_name)
      notify_audit_event(
        name: event_name,
        success: true,
        meta: { user_id: user.id }
      )
    end
  end
end
