# frozen_string_literal: true

module Loanpro
  class UpcomingPayments < Service::Base
    class UpcomingPaymentsSystemError < StandardError; end

    attribute :loan_id, presence: true

    def call
      validate!

      Servicing::UpcomingPayments.new(build_payload)
    rescue StandardError => e
      Rails.logger.error('Error in UpcomingPayments', error_message: e.message)
      raise UpcomingPaymentsSystemError, "upcoming payments error - #{e.message}"
    end

    private

    def build_payload
      upcoming_payments&.map do |autopay|
        {
          'id' => autopay['id'],
          'amount' => format('%.2f', autopay['amount']),
          'date' => LoanproHelper.parse_date(autopay['applyDate']).iso8601,
          'type' => LoanproHelper::AUTO_PAY_TYPES.invert[autopay['type']],
          'status' => LoanproHelper::AUTO_PAY_STATUS.invert[autopay['status']]
        }
      end
    end

    def autopays
      @autopays ||= Clients::LoanproApi.get_loan(loan_id, expand: 'Autopays')
    end

    def upcoming_payments
      return nil if autopays&.dig('Autopays')&.empty?

      autopays&.dig('Autopays')&.select { |autopay| autopay['status'] == LoanproHelper::AUTO_PAY_STATUS[:pending] }
    end
  end
end
