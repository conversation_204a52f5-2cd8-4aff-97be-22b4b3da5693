# frozen_string_literal: true

module Loanpro
  class PaymentHistory < Service::Base
    class PaymentHistorySystemError < StandardError; end

    attribute :loan_id, presence: true
    attribute :offset
    attribute :limit

    def call
      validate!

      Servicing::PaymentHistory.new(build_payload)
    rescue StandardError => e
      Rails.logger.error('Error in PaymentHistory', error_message: e.message)
      raise PaymentHistorySystemError, "Loanpro payment history error - #{e.message}"
    end

    private

    def build_payload
      ordered_payments&.map do |payment|
        {
          'id' => payment['id'],
          'amount' => payment['amount'],
          'date' => LoanproHelper.parse_date(payment['date'])&.iso8601,
          'info' => payment['info'],
          'status' => LoanproHelper::PAYMENT_STATUS.invert[payment['status']],
          'customer_initiated' => payment['info']&.include?('Customer Initiated Payment'),
          'interest' => payment.dig('transaction', 'paymentInterest'),
          'principal' => payment.dig('transaction', 'paymentPrincipal'),
          'after_balance' => payment['afterPrincipalBalance']
        }
      end
    end

    def ordered_payments
      ordered_payments = payment_history&.sort_by { |result| LoanproHelper.parse_date(result['date']) }&.reverse
      ordered_payments = ordered_payments.drop(@offset.to_i) if @offset.present?
      ordered_payments = ordered_payments.first(@limit.to_i) if @limit.present?

      ordered_payments
    end

    def payments
      @payments ||= Clients::LoanproApi.get_payments(loan_id)
    end

    def transactions
      @transactions ||= Clients::LoanproApi.get_transactions(loan_id)
    end

    def payment_history
      payment_index = build_payment_index
      return {} if payment_index.empty? || transactions.empty?

      attach_transactions(payment_index)
      payment_index.values
    end

    def build_payment_index
      payments&.dig('results')&.index_by { |payment| payment['id'] } || {}
    end

    def attach_transactions(index)
      (transactions['results'] || []).each do |transaction|
        index[transaction['paymentId']]&.merge!('transaction' => transaction)
      end
    end
  end
end
