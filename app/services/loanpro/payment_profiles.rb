# frozen_string_literal: true

module Loanpro
  class PaymentProfiles < Service::Base
    class PaymentProfileSystemError < StandardError; end
    attribute :loanpro_loan_id, presence: true

    def call
      validate!
      payment_profiles_data = payment_profiles_from(customer_id)
      Servicing::PaymentProfiles.new(payment_profiles_data)
    rescue StandardError => e
      Rails.logger.error('Error in PaymentProfile', error_message: e.message)
      raise PaymentProfileSystemError, "payment profile error - #{e.message}"
    end

    private

    def customer_id
      response ||= Clients::LoanproApi.get_loan(loanpro_loan_id, expand: 'Customers')
      response&.dig('Customers', 0, 'id')
    end

    def payment_profiles_from(customer_id)
      profiles = Clients::LoanproApi.fetch_primary_payment_profile(customer_id, %w[CheckingAccount])['results']
      profiles.map do |profile|
        checking_account = profile['CheckingAccount'] || {}

        Rails.logger.info("Missing CheckingAccount for Payment Profile ID: #{profile['id']}") if checking_account.empty?

        build_payment_profile(profile, checking_account)
      end
    end

    def build_payment_profile(profile, checking_account)
      {
        'id' => profile['id'],
        'isPrimary' => profile['isPrimary'],
        'isSecondary' => profile['isSecondary'],
        'title' => profile['title'],
        'type' => profile['type'],
        'checkingAccountId' => profile['checkingAccountId'],
        'active' => profile['active'],
        'visible' => profile['visible'],
        'bankName' => checking_account['bankName'],
        'accountNumber' => checking_account['accountNumber']&.to_s&.last(4),
        'routingNumber' => checking_account['routingNumber']
      }
    end
  end
end
