# frozen_string_literal: true

module Auth
  class VerifyJwtToken < BaseAuth
    include Notifier

    attribute :token, :string

    def call
      return handle_missing_token unless token

      handle_success if verify_token
    end

    def decoded_token
      @decoded_token ||= Auth::DecodeJwt.call(token:)
    rescue Auth::AuthorizationError => e
      Rails.logger.error('Auth::VerifyJwtToken#decoded_token - Error Auth::AuthorizationError', token:)
      log_exception(e, ignore_notice_error: true)
      nil
    end

    private

    def verify_token
      return handle_unauthorized if !decoded_token || decoded_token.data[:app_type] || decoded_token.data[:type]

      true
    end
  end
end
