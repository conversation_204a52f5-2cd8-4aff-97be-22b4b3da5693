# frozen_string_literal: true

module ArixOnboarding
  class UploadZippedDocsToArix < Service::Base
    include Notifier

    EVENT_NAME = 'arix_onboarding_upload_zipped_docs'

    attribute :loan_id, :string
    attr_reader :loan

    class NoArixLoanError < StandardError; end
    class FilesNotFoundError < StandardError; end

    def call
      Rails.logger.info('ArixOnboarding::UploadZippedDocsToArix - Started', loan_id:)
      @loan = ::Loan.find(loan_id)

      zip_file_names = lookup_zip_files_in_s3
      upload_all_to_arix(zip_file_names)

      notify(EVENT_NAME, success: true, meta: { loan_id: loan.id, bucket: })
      Rails.logger.info('ArixOnboarding::UploadZippedDocsToArix - Finished', loan_id:)
    rescue StandardError => e
      notify(EVENT_NAME, success: false, fail_reason: e.message, meta: { loan_id:, bucket: })

      log_exception(e)
      raise e
    end

    private

    def arix_loan_id
      @arix_loan_id ||= loan.arix_funding_status&.arix_loan_id
    end

    def bucket
      @bucket ||= Rails.application.config_for(:aws).aws_s3_bucket_name
    end

    def download_file(file_name)
      retrieved_object = s3_client.get_object(bucket:, key: file_name)

      retrieved_object.body
    end

    def lookup_zip_files_in_s3
      file_names = s3_client.list_objects_v2(bucket:, prefix:).contents.select do |file|
        Rails.logger.info("ArixOnboarding::UploadZippedDocsToArix: File found - #{file.key}", bucket:, loan_id:)
        file.key.starts_with?("#{prefix}#{loan.id}-crbDocs-")
      end.map(&:key)

      if file_names.empty?
        raise FilesNotFoundError,
              "No zipped filed found for Loan ##{loan.id} in bucket #{bucket} and path #{prefix}"
      end

      file_names
    end

    def mark_final_package_uploaded
      # TODO: We should move this internal state into loan.arix_funding_status
      loan.update!(arix_onboarding_status: ::Loan::COMPLETED)
    end

    def prefix
      @prefix ||= "#{Rails.env}/#{loan.id}/document_packages/"
    end

    def s3_client
      @s3_client ||= Aws::S3::Client.new
    end

    def upload_all_to_arix(zip_file_names)
      zip_file_names.each do |file_name|
        upload_package_to_arix!(file_name)
      end

      mark_final_package_uploaded
    end

    def upload_package_to_arix!(file_name, mime_type = 'application/zip')
      raise NoArixLoanError, "No Arix loan ID found for loan #{loan_id}" if arix_loan_id.blank?

      package_name = File.basename(file_name)
      package_content = download_file(file_name)
      Clients::ArixApi.new.upload_package!(arix_loan_id, package_name, mime_type, package_content)
    end
  end
end
