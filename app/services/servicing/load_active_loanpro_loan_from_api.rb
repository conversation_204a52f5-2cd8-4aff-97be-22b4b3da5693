# frozen_string_literal: true

# Fetches the active LoanPro loan via API and returns the `loanpro_loans` from loanpro_loan_id.
#
# Note: The returned loan may not be in a valid active loanpro_loans record nor does it guarantee it is usable.
# Valid `loanpro_loans` active loans should not be soft-deleted and must have a `til_sign_date` set.
module Servicing
  class LoadActiveLoanproLoanFromApi < Service::Base
    attribute :loan, type_for(::Loan)

    def call
      fetch_loanpro_loan
    end

    private

    def fetch_loanpro_loan
      response = SignedLoanproLoan::Lookup.call(loan:)

      return unless response[:total] == 1

      loanpro_loan_id = response.dig(:results, 0, :id)
      LoanproLoan.where(loanpro_loan_id:).order(created_at: :desc).first
    end
  end
end
