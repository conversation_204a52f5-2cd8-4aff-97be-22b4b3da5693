# frozen_string_literal: true

# Performs a lookup for an active LoanPro loan using a fallback strategy.
#
# 1. Attempts a fast search-based lookup via the LoanPro search API (eventually consistent).
# 2. If no results are found or the search API times out, falls back to the slower but
#    authoritative loan API to retrieve the most recent active loan.
#
# This handles edge cases where newly created loans are not yet indexed in the search API.
# A 30-second timeout is applied to the fallback loan API call to avoid long blocking requests.
module SignedLoanproLoan
  class Lookup < Service::Base
    attribute :loan, type_for(::Loan)

    delegate :unified_id, to: :loan

    def call
      search_result = fetch_loanpro_loan_by_search
      return search_result if search_result[:total].positive?

      fetch_loanpro_loan_by_get
    end

    private

    def format_results(loanpro_results)
      results = loanpro_results['results'] || []
      {
        results: results.map do |loan|
          { id: loan['id'] }
        end,
        total: results.count
      }
    end

    def fetch_loanpro_loan_by_get
      return @fetch_loanpro_loan_by_get if defined? @fetch_loanpro_loan_by_get

      results = Clients::LoanproApi.get_active_loanpro_loan_by(unified_id:)
      @fetch_loanpro_loan_by_get = format_results(results)
    rescue Clients::LoanproApi::TimeoutError
      @fetch_loanpro_loan_by_search = format_results({})
    end

    def fetch_loanpro_loan_by_search
      return @fetch_loanpro_loan_by_search if defined? @fetch_loanpro_loan_by_search

      results = Clients::LoanproApi.search_active_loanpro_loan_by(unified_id:)
      @fetch_loanpro_loan_by_search = format_results(results)
    rescue Clients::LoanproApi::TimeoutError
      @fetch_loanpro_loan_by_search = format_results({})
    end
  end
end
