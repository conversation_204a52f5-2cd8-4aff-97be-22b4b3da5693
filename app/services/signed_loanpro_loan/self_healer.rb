# frozen_string_literal: true

module SignedLoanproLoan
  class SelfHealer < Service::Base
    class LoanError < StandardError; end
    class LoanProError < StandardError; end
    class AmsLoanproLoanError < StandardError; end

    attribute :loan, type_for(::Loan)

    def call
      validate!

      LoanproLoan.transaction do
        ams_loanpro_loan.update!(deleted_at: nil, til_sign_date:)
        loan.loanpro_loans
            .where.not(id: ams_loanpro_loan.id)
            .find_each do |loanpro_loan|
              loanpro_loan.update!(deleted_at: Time.current, til_sign_date: nil)
            end
      end

      self
    end

    def ams_loanpro_loan
      return @ams_loanpro_loan if defined?(@ams_loanpro_loan)

      loanpro_loan_id = active_loanpro_loans.dig(:results, 0, :id)

      @ams_loanpro_loan = LoanproLoan.where(loanpro_loan_id:).order(created_at: :desc).first
    end

    def meta
      {
        ams_loanpro_loan_id: ams_loanpro_loan&.id,
        til_sign_date:,
        active_loanpro_loans_count:
      }
    end

    private

    def validate!
      super
      raise LoanError, 'Loan is not onboarded' unless loan.onboarded?
      raise LoanProError, 'No Active Loanpro Loans found' if active_loanpro_loans_count.zero?
      raise LoanProError, 'Multiple active LoanPro Loans' if active_loanpro_loans_count > 1
      raise AmsLoanproLoanError, 'AMS loanpro_loan record was not found' if ams_loanpro_loan.blank?
      raise AmsLoanproLoanError, 'No AMS loanpro_loan til_sign_date found for loan' if til_sign_date.nil?
    end

    def active_loanpro_loans_count
      active_loanpro_loans[:total]
    end

    def active_loanpro_loans
      return @active_loanpro_loans if defined?(@active_loanpro_loans)

      @active_loanpro_loans = SignedLoanproLoan::Lookup.call(loan:)
    end

    def til_sign_date
      return @til_sign_date if defined?(@til_sign_date)

      @til_sign_date = loan.loanpro_loans
                           .where.not(til_sign_date: nil)
                           .first&.til_sign_date
    end
  end
end
