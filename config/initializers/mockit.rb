# frozen_string_literal: true

unless Rails.env.production?
  Mockit.configure do |config|
    config.logger = Rails.logger
    config.storage = Rails.cache

    config.mock_classes(
      Clients::PlaidApi => Mockit::Mock::PlaidApi,
      Plaid::Webhooks::Verifier => Mockit::Mock::Verifier,
      Clients::OcrolusApi => Mockit::Mock::<PERSON><PERSON><PERSON>us<PERSON><PERSON>,
      Api::OcrolusWebhooksController => Mockit::Mock::OcrolusWebhooksController
    )
  end
end
