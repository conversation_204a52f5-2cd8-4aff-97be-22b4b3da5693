# frozen_string_literal: true

Flipper.configure do |config|
  adapter = if ENV['REDIS_URL']
              redis_instance = Redis.new(url: ENV.fetch('REDIS_URL'))
              Flipper::Adapters::Redis.new(redis_instance)
            else
              Flipper::Adapters::Memory.new
            end

  # Ref:  https://www.flippercloud.io/docs/instrumentation#advanced-setup
  #       https://github.com/wrapbook/flipper-notifications/blob/main/spec/flipper/notifications_spec.rb
  # TLDR:
  # Flipper comes with built-in instrumentation specific to Flipper Cloud.
  # By specifying ActiveSupport Instrumentation, we use the rails default
  config.default { Flipper.new(adapter, instrumenter: ActiveSupport::Notifications) }
end

existing_feature_flags = Flipper.features.map(&:name)

# Feature Switches: Core features controlled independently of standard feature flags.
# These switches are intended to be long-term, unlike temporary feature flags that will eventually be removed.
feature_switches = %w[
  enable_update_gds_with_email_validation
  enable_email_validation
  enable_dropoff_emails
  execute_pre_production_extended_dropoff
  execute_pre_production_beyond_status_update
  send_dropoff_emails
  enable_retargeting_emails
  maintenance_mode
  show_maintenance_banner
]

# Current Feature Flags: This list makes specific feature flags visible in flipper-ui immediately after deployment.
# These are typically temporary or experimental flags meant to be toggled on/off during testing or gradual rollouts.
current_feature_flags = %w[
  automated_verification_declines
  show_servicing_payment_breakdown
  credit_model_store_payment_shock_fields
  credit_model_decisioning_switch
  credit_model_arix_fields
  servicing_loanpro_loan_self_healing
  automated_good_bye_mail
]

# Add feature switches
current_feature_flags += feature_switches

# Add feature flags related to experiments
current_feature_flags += Rails.application.config_for(:experiments).keys.map do |experiment_name|
  "experiment_#{experiment_name}"
end

# Pre-initialize list of features to make them visible at flipper-ui right after deployment
current_feature_flags.each do |feature|
  # But do not overwrite existing definitions
  Flipper.disable(feature) unless existing_feature_flags.include?(feature)
end

# Remove feature flags that are no longer used
obsolete_feature_flags = existing_feature_flags - current_feature_flags
obsolete_feature_flags.each { |feature| Flipper.remove(feature) }

# add slackmoji
module Flipper
  module Notifications
    class FeatureEvent
      def summary_markdown
        msg = String.new("AMS *[#{Rails.env}]* Feature `#{feature.name}` was #{action_taken}.")

        if include_state?
          msg << ' The feature is now *fully enabled.* :large_green_circle:' if operation == 'enable'
          msg << ' The feature is now *fully disabled.* :red_circle:' if operation == 'disable'
        end

        msg
      end

      def action_taken
        case operation
        when 'add'
          'added :white_check_mark:'
        when 'clear'
          'cleared'
        when 'remove'
          'removed :x:'
        when 'enable', 'disable'
          'updated'
        else
          ''
        end
      end
    end
  end
end

# allow in all non-development, non-test environments
# sandbox, staging, production
if !Rails.env.development? && !Rails.env.test?
  Flipper::Notifications.configure do |config|
    config.enabled = true
    slack_webhook = Flipper::Notifications::Webhooks::Slack.new(url: ENV.fetch('FLIPPER_SLACK_HOOK'))

    notifier = lambda do |event:|
      slack_webhook.notify(event:)
    end

    config.notifiers = [
      notifier
    ]
  end
end
