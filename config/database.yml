default: &default
  primary: &primary
    adapter: postgresql
    host: <%= ENV['AURORA_HOST'] %>
    port: <%= ENV['AURORA_PORT'] %>
    username: <%= ENV['ABOVELENDING_DATABASE_USERNAME'] %>
    password: <%= ENV['ABOVELENDING_DATABASE_PASSWORD'] %>

development:
  primary:
    <<: *primary
    database: <%= ENV.fetch('ALP_DATABASE_NAME', 'abovelending') %>

test:
  primary:
    <<: *primary
    # TEST_ENV_NUMBER is ingested by parallel_tests allowing running tests in parallel without conflicts
    database: abovelending_test<%= ENV['TEST_ENV_NUMBER'] ? "_#{ENV['TEST_ENV_NUMBER']}" : '' %>

sandbox:
  primary:
    <<: *primary
    database: abovelending

staging:
  primary:
    <<: *primary
    database: abovelending-stage

production:
  primary:
    <<: *primary
    database: abovelending
    pool: 10
