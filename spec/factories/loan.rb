# frozen_string_literal: true

FactoryBot.define do
  factory :loan, class: '::Loan' do
    borrower                    { build(:borrower) }
    id                          { SecureRandom.uuid }
    amount                      { rand(2500..10_000) }
    purpose                     { 'testing' }
    code                        { SecureRandom.hex.first(6) }
    product_type                { [Loan::IPL_LOAN_PRODUCT_TYPE, Loan::UPL_LOAN_PRODUCT_TYPE].sample } # UPL (APL) and IPL (AGL)
    source_type                 { 'PPC' }
    unified_id                  { Faker::Number.number(digits: 8).to_s }
    request_id                  { Faker::Number.number(digits: 8).to_s }
    campaign                    { ['Bill Doctor', 'Consumer Voice'].sample if product_type == 'DM' }
    credit_score                { Faker::Number.number(digits: 3) }
    dti                         { Faker::Number.decimal(l_digits: 0) }
    anual_income                { Faker::Number.decimal(l_digits: 5) }
    verified_income             { Faker::Number.decimal(l_digits: 5) }
    bankruptcy_filed_date       { rand(1..10).years.ago.to_date }
    originating_party           { 'CRB' }
    loan_app_status             { build(:loan_app_status) }
    bypass_loan_status_history  { true }
    bypass_beyond_status_update { true }
    last_paycheck_on { 7.days.ago.to_date }
    program_duration_in_tmonths { 5 }
    contract_signing_token { nil }
  end

  trait :with_pi2_data do
    employment_status { Loan.employment_statuses.keys.sample }
    employment_pay_frecuency { Loan.employment_pay_frecuencies.keys.sample }
    monthly_housing_payment { rand(300..1000) }
  end

  trait :pending do
    loan_app_status { build(:loan_app_status, name: 'PENDING') }
  end

  trait :approved do
    loan_app_status { build(:loan_app_status, name: 'APPROVED') }
  end

  trait :expired do
    loan_app_status { build(:loan_app_status, name: 'EXPIRED') }
  end

  trait :offered do
    loan_app_status { build(:loan_app_status, name: 'OFFERED') }
  end

  trait :onboarded do
    loan_app_status { build(:loan_app_status, name: 'ONBOARDED') }
  end

  trait :front_end_declined do
    loan_app_status { build(:loan_app_status, name: 'FRONT_END_DECLINED') }
  end

  trait :investor_assigned do
    investor { build(:investor) }
  end

  factory :expired_loan, parent: :loan do
    product_type { Loan::IPL_LOAN_PRODUCT_TYPE }
    employment_status { %w[employed_full_time employed_part_time military not_employed self_employed retired other].sample }
    monthly_housing_payment { Faker::Number.between(from: 500, to: 900).to_s }
    employment_pay_frecuency { %w[weekly monthly semi_monthly biweekly].sample }
    loan_app_status do
      build(:loan_app_status, name: 'EXPIRED')
    end
  end

  factory :withdrawal_eligible_loan, parent: :loan do
    product_type { %w[DM IPL UPL].sample }
    loan_app_status do
      build(:loan_app_status, name: (LoanAppStatus::ONGOING_LOAN_STATUSES - %w[APPROVED ONBOARDED]).sample)
    end
  end

  factory :ipl_loan, parent: :loan do
    product_type { Loan::IPL_LOAN_PRODUCT_TYPE }
    source_type { %w[WEB GDS].sample }
  end

  factory :upl_loan, parent: :loan do
    code { nil }
    product_type { Loan::UPL_LOAN_PRODUCT_TYPE }
    source_type { 'BEYOND' }
  end
end
