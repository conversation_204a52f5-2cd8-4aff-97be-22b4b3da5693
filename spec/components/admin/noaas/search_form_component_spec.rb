# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Admin::Noaas::SearchFormComponent, type: :component do
  let(:form_model) do
    Admin::NoaaSearchFormModel.new(
      loan_identifier: 'abc123',
      first_name: 'john',
      last_name: 'doe',
      document_created_on: '2022-01-01'
    )
  end

  it 'renders the form' do
    rendered_component = render_inline(described_class.new(form_model:))
    expect(rendered_component).to have_selector('input[name="admin_noaa_search_form_model[loan_identifier]"][value="abc123"]')
    expect(rendered_component).to have_selector('input[name="admin_noaa_search_form_model[first_name]"][value="john"]')
    expect(rendered_component).to have_selector('input[name="admin_noaa_search_form_model[last_name]"][value="doe"]')
    expect(rendered_component).to have_selector('input[name="admin_noaa_search_form_model[document_created_on]"][value="2022-01-01"]')
  end
end
