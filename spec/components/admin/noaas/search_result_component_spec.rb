# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Admin::Noaas::SearchResultComponent, type: :component do
  let(:noaa_details) do
    [
      {
        noaa_document_name: 'noaa document',
        noaa_created_at: '2022-01-01'.to_datetime,
        loan_status_reached_at: '2022-01-02'.to_datetime,
        current_loan_status: 'BACKEND_DECLINED',
        noaa_download_url: '/download'
      },
      {
        noaa_document_name: 'noaa document 2',
        noaa_created_at: '2022-01-03'.to_datetime,
        loan_status_reached_at: nil, # older UPL NOAAs are not linked to a loan
        current_loan_status: nil,
        noaa_download_url: '/download2'
      }
    ]
  end

  it 'renders the noaa details correctly' do
    rendered_component = render_inline(described_class.new(noaa_details:))

    expect(rendered_component.text).to have_content('noaa document')
    expect(rendered_component.text).to have_content('January 01, 2022 00:00:00')
    expect(rendered_component.text).to have_content('January 02, 2022 00:00:00')
    expect(rendered_component.text).to have_content('BACKEND_DECLINED')
    expect(rendered_component).to have_selector('a[href="/download"]')

    expect(rendered_component.text).to have_content('noaa document 2')
    expect(rendered_component.text).to have_content('January 03, 2022 00:00:00')
    expect(rendered_component).to have_selector('a[href="/download2"]')
  end
end
