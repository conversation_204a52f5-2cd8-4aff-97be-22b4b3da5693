# frozen_string_literal: true

require 'rails_helper'

RSpec.describe UI::ExternalLinkDisclosureComponent, type: :component do
  include ActionView::Helpers::TagHelper

  let(:trigger_text) { 'Click me' }
  let(:external_link) { 'https://example.com' }
  let(:external_link_text) { 'Exit' }

  subject(:component) { described_class.new(trigger_text:, external_link:, external_link_text:) }

  it 'renders the trigger text' do
    rendered_component = render_inline(component)

    expect(rendered_component).to have_text(trigger_text)
  end

  it 'renders the modal' do
    rendered_component = render_inline(component)

    expect(rendered_component).to have_css('[data-controller="modal"]')
  end

  it 'renders the exit url and text' do
    rendered_component = render_inline(component)

    expect(rendered_component).to have_link(external_link_text, href: external_link)
  end
end
