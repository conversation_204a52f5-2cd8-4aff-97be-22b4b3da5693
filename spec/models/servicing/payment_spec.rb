# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Servicing::Payment do
  let(:payment_hash) do
    {
      'id' => 251_456,
      'amount' => '177.59',
      'date' => '2025-02-14T00:00:00+00:00',
      'type' => 'Unknown',
      'status' => 'success',
      'customer_initiated' => false,
      'interest' => '118.37',
      'principal' => '59.22',
      'after_balance' => '12952.07'
    }
  end

  subject { described_class.new(payment_hash) }

  it 'creates a payment object' do
    expect(subject.id).to eq(payment_hash['id'])
    expect(subject.amount).to eq(payment_hash['amount'].to_d)
    expect(subject.date).to eq(payment_hash['date'].to_date)
    expect(subject.type).to eq(payment_hash['type'])
    expect(subject.status).to eq(payment_hash['status'])
    expect(subject.customer_initiated).to eq(payment_hash['customer_initiated'])
    expect(subject.interest).to eq(payment_hash['interest'].to_d)
    expect(subject.principal).to eq(payment_hash['principal'].to_d)
    expect(subject.after_balance).to eq(payment_hash['after_balance'].to_d)
  end
end
