# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Servicing::PaymentProfile do
  let(:payment_profile) do
    {
      'id' => 55_865,
      'isPrimary' => 1,
      'isSecondary' => 0,
      'title' => 'Personal Account ********',
      'type' => 'paymentAccount.type.checking',
      'checkingAccountId' => 55_873,
      'active' => 1,
      'visible' => 1,
      'bankName' => 'Wells Fargo',
      'accountNumber' => '3333',
      'routingNumber' => '*********'
    }
  end

  subject { described_class.new(payment_profile) }

  context 'primary accounts' do
    it 'has account details' do
      profile = subject

      expect(profile.id).to eq(payment_profile['id'])
      expect(profile.primary).to eq(true)
      expect(profile.secondary).to eq(false)
      expect(profile.title).to eq(payment_profile['title'])
      expect(profile.type).to eq(payment_profile['type'])
      expect(profile.checking_account_id).to eq(55_873)
      expect(profile.active).to eq(true)
      expect(profile.visible).to eq(true)
      expect(profile.bank_name).to eq('Wells Fargo')
      expect(profile.account_number).to eq('3333')
      expect(profile.routing_number).to eq('*********')
    end
  end

  context 'secondary accounts' do
    before do
      payment_profile.merge!('isPrimary' => 0, 'isSecondary' => 1)
    end

    it 'has account details' do
      profile = subject

      expect(profile.primary).to eq(false)
      expect(profile.secondary).to eq(true)
    end
  end
end
