# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Admin::NoaaSearchFormModel, type: :model do
  describe 'validations' do
    it 'validates that at least one required field is populated' do
      subject = described_class.new

      expect(subject).not_to be_valid
      expect(subject.errors[:base]).to match_array(/Please provide either a loan identifier or at least one of: first name or last name/)

      subject.loan_identifier = 'test'

      expect(subject).to be_valid
    end

    it 'validates that loan identifier and document identifier are not both provided' do
      subject = described_class.new(loan_identifier: 'test', first_name: 'test')

      expect(subject).not_to be_valid
      expect(subject.errors[:base]).to match_array(/Please provide either a loan identifier OR document identifier, not both/)
    end
  end
end
