# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Servicing::PaymentsController, type: :request do
  include ActiveSupport::NumberHelper

  let(:code) { 'Wv1F5Q' }
  let(:service_entity) { 'bf' }
  let!(:landing_lead) { create(:landing_lead, lead_code: code) }
  let!(:borrower) { create(:borrower, email: landing_lead.email) }
  let!(:loan) { create(:loan, :onboarded, :investor_assigned, borrower:, code:, product_type: Loan::IPL_LOAN_PRODUCT_TYPE) }
  let(:session) { { code:, service_entity:, borrower_id: borrower.id, servicing_loan_payable: true } }
  let!(:loanpro_loan) { create(:loanpro_loan, loan:, til_sign_date: Date.today) }

  before do
    mock_session!(session)

    allow(Users::CheckAccountActivation).to receive(:call).and_return(false)
    allow(Servicing::LoadActiveLoanproLoanFromApi).to receive(:call).and_return(loanpro_loan)
  end

  let(:payment_history) do
    payload = [
      { 'id' => 151_456,
        'amount' => '177.59',
        'date' => 4.years.ago.iso8601,
        'info' => 'N/A',
        'status' => 'success',
        'customer_initiated' => false,
        'interest' => '118.37',
        'principal' => '59.22',
        'after_balance' => '14952.07' },
      { 'id' => 251_456,
        'amount' => '177.59',
        'date' => 3.years.ago.iso8601,
        'info' => 'scheduled payment',
        'status' => 'success',
        'customer_initiated' => false,
        'interest' => '118.37',
        'principal' => '59.22',
        'after_balance' => '12952.07' },
      { 'id' => 241_967,
        'amount' => '177.59',
        'date' => 2.years.ago.iso8601,
        'info' => 'scheduled payment',
        'status' => 'success',
        'customer_initiated' => false,
        'interest' => '124.21',
        'principal' => '53.38',
        'after_balance' => '12005.45' },
      { 'id' => 300_200,
        'amount' => '200.00',
        'date' => 1.years.ago.iso8601,
        'info' => 'customer initiated payment',
        'status' => 'voided',
        'customer_initiated' => true,
        'interest' => '150.00',
        'principal' => '50.00',
        'after_balance' => '11005.45' },
      { 'id' => 300_500,
        'amount' => '200.00',
        'date' => 2.weeks.ago.iso8601,
        'info' => 'customer initiated payment',
        'status' => 'voided',
        'customer_initiated' => true,
        'interest' => '150.00',
        'principal' => '50.00',
        'after_balance' => '10005.45' }
    ]

    Servicing::PaymentHistory.new(payload)
  end

  let(:blank_payment_history) do
    payload = []
    Servicing::PaymentHistory.new(payload)
  end

  let(:last_payment) do
    {
      payment_amount: 123.45,
      payment_principal: 23.45,
      payment_interest: 100,
      date: 1.month.ago.iso8601
    }.stringify_keys
  end

  let(:dashboard_details) do
    payload = {
      'apr' => '27.1727',
      'loan_amount' => 12_388.04,
      'loan_payment' => 351.09,
      'number_of_terms' => 71,
      'contract_date' => 1.day.ago.strftime('%F'),
      'current_due_date' => 2.weeks.from_now.strftime('%F'),
      'current_payment_due' => '0.00',
      'days_past_due' => 0,
      'loan_status_text' => 'Open',
      'number_of_remaining_terms' => 71,
      'next_payment_amount' => '351.09',
      'next_payment_date' => 2.weeks.from_now.strftime('%F'),
      'overdue_amount' => '0.00',
      'payment_frequency' => 'loan.frequency.monthly',
      'remaining_balance' => 12_902.45,
      'sub_status' => 'Good Standing',
      'sub_status_id' => 9,
      'last_payment' => last_payment,
      'borrower_name' => 'ERICA LAMBERT',
      'address' => '251 Will Circles apt 1',
      'city' => 'West Nicolefort',
      'state' => 'CA',
      'zip_code' => '94025',
      'debt_sale' => false,
      'beneficial_owner_name' => nil
    }

    Servicing::DashboardDetails.new(payload)
  end

  let(:payment_profiles) do
    payload = [
      {
        'id' => 55_865,
        'isPrimary' => 1,
        'isSecondary' => 0,
        'title' => 'Personal Account ********',
        'type' => 'paymentAccount.type.checking',
        'checkingAccountId' => 55_873,
        'active' => 1,
        'visible' => 1,
        'bankName' => 'Wells Fargo',
        'accountNumber' => '3333',
        'routingNumber' => '*********'
      },
      {
        'id' => 55_867,
        'isPrimary' => 0,
        'isSecondary' => 1,
        'title' => 'Checking Account 1231234',
        'type' => 'paymentAccount.type.checking',
        'checkingAccountId' => 55_874,
        'active' => 0,
        'visible' => 1,
        'bankName' => 'Wells Fargo',
        'accountNumber' => '55555',
        'routingNumber' => '*********'
      }
    ]

    Servicing::PaymentProfiles.new(payload)
  end

  let(:upcoming_payments) do
    payload = [
      { 'id' => 283_615, 'amount' => '981.00', 'date' => '2025-03-06', 'type' => 'recurring', 'status' => 'pending' },
      { 'id' => 287_526, 'amount' => '1317.00', 'date' => '2025-03-18', 'type' => 'single', 'status' => 'pending' }
    ]

    Servicing::UpcomingPayments.new(payload)
  end

  describe '#index' do
    before do
      allow(Loanpro::PaymentHistory).to receive(:call).and_return(payment_history)
    end

    it 'renders blank history' do
      allow(Loanpro::PaymentHistory).to receive(:call).and_return(payment_history).and_return(blank_payment_history)

      get servicing_payments_path

      expect(response).to be_successful
      expect(response.body).to include('You have made no payments yet.')
      assert_select('form', count: 0)
    end

    it 'renders full history without breakdown' do
      get servicing_payments_path

      expect(response).to be_successful
      expect(response.body).not_to include('You have made no payments yet.')
      assert_select('tbody tr', count: 5)
      assert_select('form', count: 1)

      payment_history.payments.each do |payment|
        expect(response.body).to include('Completed')
        expect(response.body).to include(number_to_currency(payment.amount))

        expect(response.body).not_to include(number_to_currency(payment.interest))
        expect(response.body).not_to include(number_to_currency(payment.principal))
        expect(response.body).not_to include(number_to_currency(payment.after_balance))
      end
    end

    it 'renders filtered history L12M' do
      get servicing_payments_path(servicing_history_range_form_model: { range: 'L12M' })

      expect(response).to be_successful
      expect(response.body).not_to include('You have made no payments yet.')
      assert_select('tbody tr', count: 1)
    end

    it 'renders filtered history L24M' do
      get servicing_payments_path(servicing_history_range_form_model: { range: 'L24M' })

      expect(response).to be_successful
      expect(response.body).not_to include('You have made no payments yet.')
      assert_select('tbody tr', count: 2)
    end

    it 'renders filtered history L36M' do
      get servicing_payments_path(servicing_history_range_form_model: { range: 'L36M' })

      expect(response).to be_successful
      expect(response.body).not_to include('You have made no payments yet.')
      assert_select('tbody tr', count: 3)
    end

    it 'handles empty param' do
      get servicing_payments_path(servicing_history_range_form_model: { range: '' })

      expect(response).to redirect_to(servicing_payments_path)
    end

    it 'handles invalid param' do
      get servicing_payments_path(servicing_history_range_form_model: { range: 'asdf' })

      expect(response).to redirect_to(servicing_payments_path)
    end

    it 'handles error message for LoanproLoan error and records metadata' do
      allow(Servicing::LoadActiveLoanproLoanFromApi).to receive(:call).and_raise(StandardError.new('Boom!'))

      get servicing_payments_path

      expect(response).to be_successful
      event = expect_request_event_record('get_servicing_payments_index')
      expect(event.metadata['api_loanpro_loan_error']).to eq('Boom!')
    end

    it 'redirects to whoops on error' do
      expect(Loanpro::PaymentHistory).to receive(:call).and_raise('Boom!')

      get servicing_payments_path

      expect_request_event_record('get_servicing_payments_index')

      expect(response).to redirect_to(whoops_servicing_dashboard_index_path(offer: code, s: service_entity))

      expect(flash[:whoops_data][:message]).to eq('Boom!')
      expect(flash[:whoops_data][:request_id]).not_to be_blank
    end

    context 'when servicing_loanpro_loan_from_api Flipper is enabled', with_feature_flag: :servicing_loanpro_loan_from_api do
      it 'is successful' do
        get servicing_payments_path

        expect(response).to be_successful
      end

      it 'uses the loanpro loans loanpro_loans record' do
        loanpro_loan2 = create(:loanpro_loan)

        expect(Servicing::LoadActiveLoanproLoanFromApi).to receive(:call).and_return(loanpro_loan2)
        allow(Loanpro::PaymentHistory)
          .to receive(:call)
          .with(loan_id: loanpro_loan2.loanpro_loan_id)
          .and_return(payment_history)

        get servicing_payments_path

        expect(response).to be_successful
      end
    end

    context 'when show_servicing_payment_breakdown is active' do
      before do
        Flipper.enable(:show_servicing_payment_breakdown)
      end

      it 'includes payment breakdown' do
        get servicing_payments_path

        expect(response).to be_successful
        expect(response.body).not_to include('You have made no payments yet.')
        assert_select('tbody tr', count: 5)
        assert_select('form', count: 1)

        payment_history.payments.each do |payment|
          expect(response.body).to include('Completed')
          expect(response.body).to include(number_to_currency(payment.amount))

          expect(response.body).to include(number_to_currency(payment.interest))
          expect(response.body).to include(number_to_currency(payment.principal))
          expect(response.body).to include(number_to_currency(payment.after_balance))
        end
      end
    end
  end

  describe '#new' do
    before do
      allow(Loanpro::DashboardDetails).to receive(:call).and_return(dashboard_details)
      allow(Loanpro::PaymentProfiles).to receive(:call).and_return(payment_profiles)
    end

    it 'renders' do
      get new_servicing_payment_path

      expect(response).to be_successful
    end

    it 'renders past due amount if loan is past due' do
      allow(dashboard_details).to receive(:overdue_amount).and_return(150.00)
      allow(dashboard_details).to receive(:past_due?).and_return(true)

      get new_servicing_payment_path

      expect(response).to be_successful
      expect(response.body).to include('Past Due Amount')
    end

    it 'does not render last payment amount if unapplicable' do
      allow(dashboard_details).to receive(:next_payment_amount).and_return(last_payment['payment_amount'])

      get new_servicing_payment_path

      expect(response).to be_successful
      expect(response.body).not_to include('Last Payment Amount')
    end

    it 'redirects to whoops on error' do
      expect(Loanpro::DashboardDetails).to receive(:call).and_raise('Boom!')

      get new_servicing_payment_path

      expect_request_event_record('get_servicing_payments_new')

      expect(response).to redirect_to(whoops_servicing_dashboard_index_path(offer: code, s: service_entity))

      expect(flash[:whoops_data][:message]).to eq('Boom!')
      expect(flash[:whoops_data][:request_id]).not_to be_blank
    end

    it 'redirects to dashboard if loan is not payable' do
      mock_session!(session.except(:servicing_loan_payable))

      get new_servicing_payment_path

      expect(response).to redirect_to(servicing_dashboard_index_path)
    end
  end

  describe '#create' do
    let(:valid_attributes) do
      {
        payment_date: 3.days.from_now,
        payment_profile_id: payment_profiles.active.id,
        payment_time_zone: 'America/Los_Angeles',
        payment_amount: 100.00
      }
    end

    before do
      allow(Loanpro::DashboardDetails).to receive(:call).and_return(dashboard_details)
      allow(Loanpro::PaymentProfiles).to receive(:call).and_return(payment_profiles)
    end

    context 'when the form submission is valid' do
      it 'shows the warning modal if duplicate' do
        allow(Loanpro::CreatePayment).to receive(:call)

        mock_session!(session.merge(last_scheduled_payment_amount: valid_attributes[:payment_amount], last_scheduled_payment_date: valid_attributes[:payment_date]))

        turbo_post path: servicing_payments_path, params: { servicing_new_payment_form_model: valid_attributes }

        expect(Loanpro::CreatePayment).not_to have_received(:call)
        expect(response).to be_successful
        expect(response.body).to include('recently scheduled a payment for the same amount and processing date')
        expect(response).to render_template(:new)
      end

      it 'when clicking through the modal, creates a payment and redirects to success' do
        allow(Loanpro::CreatePayment).to receive(:call)
        mock_session!(session.merge(last_scheduled_payment_amount: valid_attributes[:payment_amount], last_scheduled_payment_date: valid_attributes[:payment_date]))

        turbo_post path: servicing_payments_path, params: { servicing_new_payment_form_model: valid_attributes.merge(override_duplicate_warning: true) }

        expect(Loanpro::CreatePayment).to have_received(:call)
        expect(response).to redirect_to(success_servicing_payments_path)
      end

      it 'creates a payment and redirects to success' do
        allow(Loanpro::CreatePayment).to receive(:call)

        turbo_post path: servicing_payments_path, params: {
          servicing_new_payment_form_model: valid_attributes
        }

        expect(Loanpro::CreatePayment).to have_received(:call)
        expect(response).to redirect_to(success_servicing_payments_path)
      end

      it 'allows a payment on the day after the contract date' do
        allow(Loanpro::CreatePayment).to receive(:call)

        turbo_post path: servicing_payments_path, params: {
          servicing_new_payment_form_model: valid_attributes.merge(payment_date: (dashboard_details.contract_date + 1.day).strftime('%F'))
        }

        expect(Loanpro::CreatePayment).to have_received(:call)
        expect(response).to redirect_to(success_servicing_payments_path)
      end
    end

    context 'when the form submission is invalid' do
      it 'disallows a payment on the contract date' do
        turbo_post path: servicing_payments_path, params: {
          servicing_new_payment_form_model: valid_attributes.merge(payment_date: dashboard_details.contract_date)
        }

        expect(response).to render_template(:new)
        expect(response).not_to be_successful
        expect(assigns(:form_model).errors).not_to be_empty
      end

      it 're-renders the form with errors' do
        turbo_post path: servicing_payments_path, params: {
          servicing_new_payment_form_model: valid_attributes.merge(payment_amount: dashboard_details.payoff_amount + 1)
        }

        expect(response).to render_template(:new)
        expect(response).not_to be_successful
        expect(assigns(:form_model).errors).not_to be_empty
      end
    end

    it 'redirects to whoops on error' do
      allow(Loanpro::DashboardDetails).to receive(:call).and_raise('Boom!')

      turbo_post path: servicing_payments_path, params: { servicing_new_payment_form_model: valid_attributes }

      expect_request_event_record('post_servicing_payments_create')

      expect(response).to redirect_to(whoops_servicing_dashboard_index_path(offer: code, s: service_entity))

      expect(flash[:whoops_data][:message]).to eq('Boom!')
      expect(flash[:whoops_data][:request_id]).not_to be_blank
    end
  end

  describe '#success' do
    before do
      allow(Loanpro::UpcomingPayments).to receive(:call)
      allow(Loanpro::PaymentProfiles).to receive(:call).and_return(payment_profiles)
    end

    it 'redirects to #new if not referred by #new' do
      get success_servicing_payments_path

      expect(response).to redirect_to(new_servicing_payment_path)
    end

    it 'renders' do
      allow(Loanpro::UpcomingPayments).to receive(:call).and_return(upcoming_payments)

      get success_servicing_payments_path, headers: { 'Referer' => new_servicing_payment_path }

      expect(response).to be_successful
    end

    it 'redirects to whoops on error' do
      expect(Loanpro::UpcomingPayments).to receive(:call).and_return(upcoming_payments)
      expect(Loanpro::PaymentProfiles).to receive(:call).and_raise('Boom!')

      get success_servicing_payments_path, headers: { 'Referer' => new_servicing_payment_path }

      expect_request_event_record('get_servicing_payments_success')

      expect(response).to redirect_to(whoops_servicing_dashboard_index_path(offer: code, s: service_entity))

      expect(flash[:whoops_data][:message]).to eq('Boom!')
      expect(flash[:whoops_data][:request_id]).not_to be_blank
    end
  end

  describe '#destroy' do
    let(:payment_id) { SecureRandom.random_number }

    it 'cancels payment successfully' do
      allow(Loanpro::CancelPayment).to receive(:call).and_return(true)

      turbo_delete path: servicing_payment_path(payment_id)

      expect(response).to redirect_to(servicing_dashboard_index_path)
    end

    it 'fails to cancel payment' do
      allow(Loanpro::CancelPayment).to receive(:call).and_return(false)

      turbo_delete path: servicing_payment_path(payment_id)

      expect(response).not_to be_successful
      expect(response.body).to include('Please call us')
    end

    it 'redirects to whoops on error' do
      expect(Loanpro::CancelPayment).to receive(:call).and_raise('Boom!')

      turbo_delete path: servicing_payment_path(payment_id)

      expect_request_event_record('delete_servicing_payments_destroy')

      expect(response).to redirect_to(whoops_servicing_dashboard_index_path(offer: code, s: service_entity))

      expect(flash[:whoops_data][:message]).to eq('Boom!')
      expect(flash[:whoops_data][:request_id]).not_to be_blank
    end
  end
end
