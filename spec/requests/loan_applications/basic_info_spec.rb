# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanApplications::BasicInfoController, type: :request do
  include ActiveSupport::Testing::TimeHelpers

  let(:code) { 'abc123' }
  let(:session) { { code:, service_entity: 'bf' } }

  before { mock_trustpilot_summary_response }

  describe 'routing' do
    it_behaves_like 'an enforced originations funnel route', { current_path: :basic_info_loan_applications_path, unauthenticated: false }
  end

  describe '#basic_info' do
    let(:query) { { offer: code, s: 'bf' } }

    it 'renders the basic_info page' do
      create(:lead, code:)
      create(:landing_lead, lead_code: code)

      get basic_info_loan_applications_path(query)

      expect(response).to render_template(:basic_info)

      expect_request_event_record

      expect(request.session[:code]).to eq('abc123')
      expect(request.session[:service_entity]).to eq('bf')
    end

    it 'redirects to the intake page if no lead exists' do
      create(:landing_lead, lead_code: code)

      get basic_info_loan_applications_path(query)

      expected_redirect = intake_loan_applications_path(query)
      expect(response).to redirect_to(expected_redirect)

      event = expect_request_event_record
      expect(event.metadata['redirected_to']).to eq(expected_redirect)
    end

    it 'redirects to the intake page if no landing lead exists' do
      create(:lead, code:)

      get basic_info_loan_applications_path(query)

      expected_redirect = intake_loan_applications_path(query)
      expect(response).to redirect_to(expected_redirect)

      event = expect_request_event_record
      expect(event.metadata['redirected_to']).to eq(expected_redirect)
    end

    it 'does not redirect to the intake page for expired UPL loans' do
      borrower = create(:borrower)
      create(:upl_loan, :expired, borrower:)
      mock_session!(session.merge(borrower_id: borrower.id))

      get basic_info_loan_applications_path(query)

      expect(response).to render_template(:basic_info)

      expect_request_event_record
    end
  end

  describe '#basic_info_create' do
    let(:payload) do
      {
        loan_applications_basic_info_form_model: {
          first_name: 'John',
          last_name: 'Doe',
          phone_number: '(*************',
          address_apt: Faker::Address.secondary_address,
          address_street: Faker::Address.street_address,
          city: Faker::Address.city,
          date_of_birth: Faker::Date.birthday.iso8601,
          password: Faker::Internet.password(special_characters: true),
          state: Faker::Address.state_abbr,
          zip_code: Faker::Address.zip_code[0...5]
        }
      }
    end

    let!(:lead) { create(:lead, code:) }
    let!(:landing_lead) { create(:landing_lead, lead_code: code) }

    context 'when a valid form is submitted' do
      let!(:terms_template) { create(:doc_template, type: 'TERMS_OF_USE') }
      let!(:esign_template) { create(:doc_template, type: 'ESIGN_ACT_CONSENT') }
      let!(:privacy_template) { create(:doc_template, type: 'PRIVACY_POLICY') }
      let!(:credit_auth_template) { create(:doc_template, type: 'CREDIT_PROFILE_AUTHORIZATION') }

      before do
        allow(Users::SendWelcomeEmail).to receive(:call)
        allow(Users::CreateUser).to receive(:call).and_call_original
        allow(Gds::NewLoanApplicationJob).to receive(:perform_async)
        mock_session!(session)
      end

      it 'creates necessary records' do
        turbo_post path: basic_info_create_loan_applications_path, params: payload

        loan = Loan.find_by(code:)

        expect(loan).to be_present
        expect(loan.borrower).to be_present
        expect(loan.borrower.latest_borrower_info).to be_present

        expect(Gds::NewLoanApplicationJob).to have_received(:perform_async).with(loan.id)
        expect(Users::CreateUser).to have_received(:call)

        expected_redirect = additional_info_loan_applications_path(offer: session[:code], s: session[:service_entity])
        expect(response).to redirect_to(expected_redirect)

        event = expect_request_event_record
        expect(event.metadata['is_valid']).to be_truthy
        expect(event.metadata['redirected_to']).to eq(expected_redirect)
      end

      it 'redirects to the active account page if an onboarded loan is present' do
        create(:loan,
               code: lead.code,
               product_type: Lead::TYPES[:IPL],
               loan_app_status_id: LoanAppStatus.id(LoanAppStatus::ONBOARDED_STATUS))

        turbo_post path: basic_info_create_loan_applications_path, params: payload

        expected_redirect = active_application_exit_pages_path(offer: session[:code], s: session[:service_entity])
        expect(response).to redirect_to(expected_redirect)

        event = expect_request_event_record
        expect(event.metadata['redirected_to']).to eq(expected_redirect)
        expect(event.metadata['is_onboarded_status']).to be_truthy
      end

      it 'redirects to the active account page if an approved loan is present' do
        create(:loan,
               code: lead.code,
               product_type: Lead::TYPES[:IPL],
               loan_app_status_id: LoanAppStatus.id(LoanAppStatus::APPROVED_STATUS))

        turbo_post path: basic_info_create_loan_applications_path, params: payload

        expected_redirect = active_application_exit_pages_path(offer: session[:code], s: session[:service_entity])
        expect(response).to redirect_to(expected_redirect)

        event = expect_request_event_record
        expect(event.metadata['redirected_to']).to eq(expected_redirect)
        expect(event.metadata['is_approved_status']).to be_truthy
      end

      it 'redirects to the resume page if an active application is in-flight' do
        create(:loan,
               code: lead.code,
               product_type: Lead::TYPES[:IPL],
               loan_app_status_id: LoanAppStatus.id(LoanAppStatus::OFFERED_STATUS))

        turbo_post path: basic_info_create_loan_applications_path, params: payload

        expected_redirect = resume_borrowers_path
        expect(response).to redirect_to(expected_redirect)

        event = expect_request_event_record
        expect(event.metadata['redirected_to']).to eq(expected_redirect)
        expect(event.metadata['has_in_flight_loan']).to be_truthy
      end

      it 'redirects to the whoops page if an unexpected error occurs' do
        expect_any_instance_of(LoanApplications::Pi1).to receive(:call).and_raise('Boom!')

        turbo_post path: basic_info_create_loan_applications_path, params: payload

        expected_redirect = whoops_exit_pages_path(offer: session[:code], s: session[:service_entity])
        expect(flash[:whoops_data][:message]).to eq('Boom!')
        expect(flash[:whoops_data][:request_id]).not_to be_blank

        expect(response).to redirect_to(expected_redirect)

        expect_request_event_record
      end

      it 'redirect to the whoops page for expired UPL loans' do
        borrower = create(:borrower)
        create(:upl_loan, :expired, borrower:)
        mock_session!(borrower_id: borrower.id)

        turbo_post path: basic_info_create_loan_applications_path, params: payload

        expect(flash[:whoops_data][:message]).to eq('Borrower has a UPL loan in flight.')
        expect(flash[:whoops_data][:request_id]).not_to be_blank

        expect(response).to redirect_to(whoops_exit_pages_path)

        expect_request_event_record
      end

      context 'with spouse information' do
        before do
          payload[:loan_applications_basic_info_form_model].merge!(
            married: true,
            spouse_first_name: 'Jane',
            spouse_last_name: 'Doe',
            spouse_different_address: true,
            spouse_address_street: '456 Spouse St',
            spouse_city: 'Spouse City',
            spouse_state: 'TX',
            spouse_zip_code: '75001'
          )
        end

        it 'creates necessary records with all the data' do
          turbo_post path: basic_info_create_loan_applications_path, params: payload

          loan = Loan.find_by(code:)
          info = loan&.borrower&.latest_borrower_info

          expect(loan).to be_present
          expect(loan.borrower).to be_present
          expect(loan.borrower.latest_borrower_info).to be_present

          expect(info.spouse_first_name).to be_present
          expect(info.spouse_last_name).to be_present
          expect(info.spouse_address_street).to be_present
          expect(info.spouse_city).to be_present
          expect(info.spouse_state).to be_present
          expect(info.spouse_zip_code).to be_present
        end
      end
    end

    context 'when an invalid form is submitted' do
      before do
        mock_session!(session)

        payload[:loan_applications_basic_info_form_model].merge!(
          address_street: "#{Faker::Address.street_address} pobox 102"
        )
      end

      it 'with address containing PO Box' do
        turbo_post path: basic_info_create_loan_applications_path, params: payload

        expect(response).to have_http_status(:unprocessable_entity)
        expect(response).to render_template(:basic_info)

        event = expect_request_event_record
        expect(event.metadata['is_form_valid']).to be_falsey
      end

      it 'with a corrupt payload' do
        turbo_post path: basic_info_create_loan_applications_path, params: {}

        expect(response).to have_http_status(:unprocessable_entity)
        expect(response).to render_template(:basic_info)
      end
    end
  end
end
