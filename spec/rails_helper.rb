# frozen_string_literal: true

# This file is copied to spec/ when you run 'rails generate rspec:install'
unless ENV['SIMPLECOV']&.casecmp('OFF')&.zero?
  require 'simplecov'
  SimpleCov.minimum_coverage 100
  SimpleCov.refuse_coverage_drop :line
  SimpleCov.start 'rails' do
    add_filter '/spec'

    # rails-generated superclass files
    add_filter '/app/channels/application_cable/channel.rb'
    add_filter '/app/channels/application_cable/connection.rb'
    add_filter '/app/jobs/application_job.rb'
    add_filter '/app/mailers/application_mailer.rb'
    add_filter '/app/models/application_record.rb'
    add_filter '/bundle/'
    add_filter '/lib/tasks/'

    # Development specific classes
    add_filter 'lib/ext/local'
  end
end
require 'spec_helper'
require 'log_helper'
require 'trust_pilot_mock_helper'

ENV['RAILS_ENV'] ||= 'test'
ENV['DISABLE_FARADAY_LOGGING'] = 'true'
ENV['ENABLE_LOCAL_WEBHOOKS'] = 'false'

# Prevent running tests when the Redis URL points to an external server (e.g. the staging or sandbox Redis server).
# Doing so has the potential to unexpectedly delete all Flipper flags in a shared environment.
permitted_redis_hosts = %w[localhost host.docker.internal]
%w[REDIS_URL REDIS_URI].each do |redis_variable|
  next unless ENV[redis_variable] && permitted_redis_hosts.none? { |hostname| ENV[redis_variable].include?(hostname) }

  raise "## WARNING ##\nThe #{redis_variable} environment variable points to an external server " \
        "(#{ENV.fetch(redis_variable, nil)}). Update this environment variables to point to ote one of the following " \
        "hosts:\n  - #{permitted_redis_hosts.join("\n  - ")}"
end

require_relative '../config/environment'
# Prevent database truncation if the environment is production
abort('The Rails environment is running in production mode!') if Rails.env.production?
require 'rspec/rails'
# Add additional requires below this line. Rails is not loaded until this point!
require 'webmock/rspec'
require 'factory_bot_rails'
require 'sidekiq/testing'
require 'rubocop/rspec/support'
require 'view_component/test_helpers'
require 'view_component/system_test_helpers'
require 'capybara/rspec'

RSpec::Sidekiq.configure do |config|
  config.warn_when_jobs_not_processed_by_sidekiq = false
end

Sidekiq::Testing.fake!
Sidekiq::Testing.server_middleware do |chain|
  chain.add Sidekiq::Batch::Server
end

Dir[Rails.root.join('spec/support/**/*.rb')].each { |f| require f }

# Requires supporting ruby files with custom matchers and macros, etc, in
# spec/support/ and its subdirectories. Files matching `spec/**/*_spec.rb` are
# run as spec files by default. This means that files in spec/support that end
# in _spec.rb will both be required and run as specs, causing the specs to be
# run twice. It is recommended that you do not name files matching this glob to
# end with _spec.rb. You can configure this pattern with the --pattern
# option on the command line or in ~/.rspec, .rspec or `.rspec-local`.
#
# The following line is provided for convenience purposes. It has the downside
# of increasing the boot-up time by auto-requiring all files in the support
# directory. Alternatively, in the individual `*_spec.rb` files, manually
# require only the support files necessary.
#
# Dir[Rails.root.join('spec', 'support', '**', '*.rb')].sort.each { |f| require f }

# Checks for pending migrations and applies them before tests are run.
# If you are not using ActiveRecord, you can remove these lines.
begin
  ActiveRecord::Migration.maintain_test_schema!
rescue ActiveRecord::PendingMigrationError => e
  abort e.to_s.strip
end

# add Loan App Status for all shards
LoanAppStatus::ID_TO_NAME[1..].each.with_index(1) do |name, id|
  # Before we create a new record, we need to check if there is already
  # a record with the same name but mismatching id, if we find one we need to
  # delete it and create a new one with the correct id
  existing_loan_app_status = LoanAppStatus.find_by(id:)
  next if existing_loan_app_status&.name == name

  existing_loan_app_status&.destroy!
  LoanAppStatus.create(id:, name:)
end

RSpec.configure do |config|
  # Remove this line if you're not using ActiveRecord or ActiveRecord fixtures
  config.fixture_paths = ["#{Rails.root}/spec/fixtures"]

  # If you're not using ActiveRecord, or you'd prefer not to run each of your
  # examples within a transaction, remove the following line or assign false
  # instead of true.
  config.use_transactional_fixtures = true

  # You can uncomment this line to turn off ActiveRecord support entirely.
  # config.use_active_record = false

  # RSpec Rails can automatically mix in different behaviours to your tests
  # based on their file location, for example enabling you to call `get` and
  # `post` in specs under `spec/controllers`.
  #
  # You can disable this behaviour by removing the line below, and instead
  # explicitly tag your specs with their type, e.g.:
  #
  #     RSpec.describe UsersController, type: :controller do
  #       # ...
  #     end
  #
  # The different available types are documented in the features, such as in
  # https://relishapp.com/rspec/rspec-rails/docs
  config.infer_spec_type_from_file_location!

  # Filter lines from Rails gems in backtraces.
  config.filter_rails_from_backtrace!
  # arbitrary gems may also be filtered via:
  # config.filter_gems_from_backtrace("gem name")

  ActionDispatch::TestResponse.include ResponseHelper

  config.include ActiveJob::TestHelper
  config.include ApiEventHelper
  config.include Capybara::RSpecMatchers, type: :component
  config.include FactoryBot::Syntax::Methods
  config.include LogHelper
  config.include NotifierHelper
  config.include RequestEventHelper, type: :controller
  config.include RequestEventHelper, type: :request
  config.include RuboCop::RSpec::ExpectOffense
  config.include SessionHelper, type: :request
  config.include Shoulda::Matchers::ActiveModel, type: :service
  config.include TrustPilotMockHelper
  config.include TurboStreamHelper, type: :request
  config.include ViewComponent::SystemTestHelpers, type: :component
  config.include ViewComponent::TestHelpers, type: :component

  config.before :each, type: :request do
    host! 'funnel-test.abovelending.com'
  end

  config.before :example, type: :request do
    @old_perform_enqueued_jobs = ActiveJob::Base.queue_adapter.perform_enqueued_jobs
    @old_perform_enqueued_at_jobs = ActiveJob::Base.queue_adapter.perform_enqueued_at_jobs
    ActiveJob::Base.queue_adapter.perform_enqueued_jobs = true
    ActiveJob::Base.queue_adapter.perform_enqueued_at_jobs = true
  end

  config.after :example, type: :request do
    ActiveJob::Base.queue_adapter.perform_enqueued_jobs = @old_perform_enqueued_jobs
    ActiveJob::Base.queue_adapter.perform_enqueued_at_jobs = @old_perform_enqueued_at_jobs
    Faker::UniqueGenerator.clear
  end

  config.before(:each) do
    Current.reset
    ObservabilityAttributes.reset

    # NOTE:  We use certain feature flags to allow very special case
    #        off-by-default-except-in-production behaviors to be turned
    #        on in lower environments.  We'd like to ensure that these
    #        also remain on in test because otherwise we increase the risk
    #        of silent failure in test as well as lower environments.
    Flipper.enable(:execute_pre_production_beyond_status_update)
    Flipper.enable(:execute_pre_production_extended_dropoff)
  end

  config.after(:each) do
    Sidekiq::Worker.clear_all
  end

  config.include Shoulda::Matchers::ActiveRecord, type: :model
end

Shoulda::Matchers.configure do |config|
  config.integrate do |with|
    with.test_framework :rspec
    with.library :rails
  end
end

RSpec::Matchers.define_negated_matcher :not_change, :change
