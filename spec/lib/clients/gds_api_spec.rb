# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Clients::GdsApi do
  let(:config) { Rails.application.config_for(:gds_api) }
  let(:access_token) { 'access_token' }
  let(:gds_base_url) { "#{config.base_url!}#{config.gds_path}" }
  let(:gds_auth_base_url) { config.auth_base_url! }
  let(:gds_auth_mock_response) { { access_token: }.to_json }
  let(:gds_response) { { example: 'json' } }
  let(:cache_key) { config.access_token_cache_key! }
  let(:request_id) { 'request_id' }
  let(:loan_app) { build(:gds_api_loan_application) }
  let(:bank_details) { build(:gds_api_bank_details) }
  let(:borrower) { build(:gds_api_borrower) }
  let(:loan_app) { build(:gds_api_loan_application) }
  let(:above_loan_app) { build(:loan) }
  let(:documents) { build_list(:gds_api_document, 3) }
  let(:app_status) { loan_app.app_status }
  let(:product_type) { 'IPL' }
  let(:task_id) { 'task_id' }
  let(:offer_id) { 'offer_id' }

  before do
    stub_request(:post, "#{gds_auth_base_url}/oauth2/token")
      .to_return(status: 200, body: gds_auth_mock_response)
    stub_request(:post, gds_base_url)
      .to_return(status: 200, body: gds_response.to_json)
    allow(Rails.cache).to receive(:write).and_call_original
    allow(described_class).to receive(:retrieve_access_token).and_call_original
  end

  after do
    Rails.cache.delete(cache_key)
  end

  describe 'without token' do
    before do
      # ensure there is no key
      Rails.cache.delete(cache_key)
    end

    it 'fetches a token' do
      described_class.new_loan_app(product_type: 'IPL', loan_app:, borrower:)
      expect(a_request(:post, "#{gds_auth_base_url}/oauth2/token")).to have_been_made
      expect(Rails.cache).to have_received(:write).with(cache_key, access_token, a_hash_including(expires_in: 55.minutes))
    end
  end

  describe 'with token' do
    before do
      Rails.cache.write(cache_key, access_token, expires_in: 1.hour)
    end

    it 'does not fetch a token' do
      described_class.new_loan_app(product_type: 'IPL', loan_app:, borrower:)
      expect(a_request(:post, "#{gds_auth_base_url}/oauth2/token")).to_not have_been_made
    end

    describe '.file_connection' do
      let(:configuration) { Rails.application.config_for(:gds_api) }
      let(:body_mock) { double('ResponseBody', body: true) }
      let(:response_mock) { double('FaradayResponse', get: body_mock) }
      let(:faraday_args) do
        { headers: described_class.public_headers, url: configuration.base_url }
      end

      before do
        expect(Faraday).to receive(:new).with(faraday_args).and_return(response_mock)
      end

      it 'returns true if request success' do
        described_class.download_file('hi')
      end
    end

    describe '.public_headers' do
      subject { described_class.public_headers }

      it 'returns true if request success' do
        expect(subject).to eq({ 'Authorization' => "Bearer #{access_token}" })
      end
    end

    describe '.active' do
      subject { described_class.active? }

      it 'returns true if request success' do
        expect(subject).to eq(true)
      end

      context 'no connection' do
        before do
          allow(described_class).to receive(:send_request).with('newLoanApp').and_raise(StandardError)
        end

        it 'returns false' do
          expect(subject).to eq(false)
        end
      end
    end

    describe 'GDS returns an error' do
      subject { described_class.new_loan_app(product_type:, loan_app:, borrower:) }

      before do
        stub_request(:post, gds_base_url)
          .to_return(body: { 'error_message' => 'Test error', status: 200 }.to_json)

        allow(ExceptionLogger).to receive(:error)
      end

      it 'logs the error' do
        subject

        event = expect_api_event_record(name: 'gds_new_loan_app')
        expect(event.metadata['call_type']).to eq('newLoanApp')
        expect(event.metadata['attempt']).to eq(1)
        expect(event.response['error_message']).to eq('Test error')
      end
    end

    describe '.new_loan_app' do
      subject { described_class.new_loan_app(product_type:, loan_app:, borrower:) }
      describe 'with proper data objects' do
        it 'sends out a request' do
          subject
          expect(a_request(:post, gds_base_url).with(body: {
                                                       call_type: 'newLoanApp',
                                                       product_type:,
                                                       loan_app: loan_app.attributes.as_json,
                                                       borrower: borrower.attributes.as_json
                                                     })).to have_been_made
        end
      end

      describe 'with wrong loan_app object' do
        let(:loan_app) do
          {
            wrong: 'type'
          }
        end

        it 'raises an error' do
          expect { subject }.to raise_error("loan_app must be an instance of #{described_class::LoanApplication}")
        end
      end

      describe 'with wrong borrower object' do
        let(:borrower) do
          {
            wrong: 'type'
          }
        end

        it 'raises an error' do
          expect { subject }.to raise_error("borrower must be an instance of #{described_class::Borrower}")
        end
      end
    end

    describe '.patch_loan_app' do
      subject { described_class.patch_loan_app(request_id:, product_type:, loan_app:, borrower:) }
      describe 'with proper data objects' do
        it 'sends out a request' do
          subject
          expect(a_request(:post, gds_base_url).with(body: {
                                                       call_type: 'patchLoanApp',
                                                       request_id:,
                                                       product_type:,
                                                       loan_app: loan_app.attributes.as_json,
                                                       borrower: borrower.attributes.as_json
                                                     })).to have_been_made
        end
      end

      describe 'with wrong loan_app object' do
        let(:loan_app) do
          {
            wrong: 'type'
          }
        end

        it 'raises an error' do
          expect { subject }.to raise_error("loan_app must be an instance of #{described_class::LoanApplication}")
        end
      end

      describe 'with wrong borrower object' do
        let(:borrower) do
          {
            wrong: 'type'
          }
        end

        it 'raises an error' do
          expect { subject }.to raise_error("borrower must be an instance of #{described_class::Borrower}")
        end
      end
    end

    describe '.get_offers' do
      subject { described_class.get_offers(request_id:, loan: above_loan_app, experiment_cohort:) }
      let(:experiment_cohort) { %w[nil champion challenger].sample }

      it 'sends out a request' do
        subject
        expect(a_request(:post, gds_base_url).with(body: {
                                                     call_type: 'getOffers',
                                                     request_id:,
                                                     loan_app: {
                                                       app_status: above_loan_app.loan_app_status.name
                                                     },
                                                     product_type: above_loan_app.product_type,
                                                     credit_test_1: experiment_cohort
                                                   })).to have_been_made
        event = expect_api_event_record(name: 'gds_get_offers')
        expect(event.metadata['call_type']).to eq('getOffers')
        expect(event.metadata['attempt']).to eq(1)
        expect(event.response).to include('example')
      end
    end

    describe '.upl_apply' do
      subject { described_class.upl_apply(application: upl_application) }

      let(:upl_application) do
        {
          amount: Faker::Number.decimal,
          loan_purpose: Ams::Api::Loans::UplApplyForLoan::LOAN_PURPOSES.sample,
          first_name: Faker::Name.first_name,
          last_name: Faker::Name.last_name,
          address_street: Faker::Address.street_address,
          city: Faker::Address.city,
          state_code: Faker::Address.state_abbr,
          zip_code: Faker::Address.zip.first(5),
          phone_number: Faker::PhoneNumber.phone_number.gsub(/[^0-9]/i, '').first(10),
          email: Faker::Internet.email,
          date_of_birth: Faker::Date.birthday.strftime('%m-%d-%Y'),
          income: Faker::Number.decimal,
          monthly_housing_payment: Faker::Number.decimal,
          ssn: Faker::Number.number(digits: 9).to_s,
          oppId: SecureRandom.uuid
        }
      end

      it 'sends out a request' do
        subject
        expect(a_request(:post, gds_base_url).with(body: {
                                                     call_type: 'getOffers',
                                                     product_type: 'UPL',
                                                     borrower: {
                                                       first_name: upl_application[:first_name],
                                                       last_name: upl_application[:last_name],
                                                       address_street: upl_application[:address_street],
                                                       city: upl_application[:city],
                                                       state: upl_application[:state_code],
                                                       zip_code: upl_application[:zip_code],
                                                       phone_number: upl_application[:phone_number],
                                                       email: upl_application[:email],
                                                       date_of_birth: upl_application[:date_of_birth],
                                                       income: upl_application[:income],
                                                       monthly_housing_payment: upl_application[:monthly_housing_payment],
                                                       ssn: upl_application[:ssn]
                                                     },
                                                     loan_app: {
                                                       amount: upl_application[:amount],
                                                       loan_purpose: upl_application[:loan_purpose]
                                                     }
                                                   })).to have_been_made
      end

      it 'uses a placeholder SSN value when none is specified within the application' do
        upl_application[:ssn] = nil
        subject
        expect(a_request(:post, gds_base_url).with(body: {
                                                     call_type: 'getOffers',
                                                     product_type: 'UPL',
                                                     borrower: a_hash_including('ssn' => '000000000'),
                                                     loan_app: {
                                                       amount: upl_application[:amount],
                                                       loan_purpose: upl_application[:loan_purpose]
                                                     }
                                                   })).to have_been_made
      end
    end

    describe '.sync_status' do
      subject { described_class.sync_status(request_id:, product_type:, status:, decision_reason_number:) }
      let(:decision_reason_number) { nil }

      describe 'with proper data objects' do
        let(:status) { (LoanAppStatus::ID_TO_NAME - %w[NONE BANK_SUBMIT]).sample }

        it 'sends out a request' do
          subject
          expect(a_request(:post, gds_base_url).with(body: {
                                                       call_type: 'syncStatus',
                                                       request_id:,
                                                       product_type:,
                                                       loan_app: { app_status: status }.as_json
                                                     })).to have_been_made
        end
      end

      describe 'when the BANK_SUBMIT status is provided' do
        let(:status) { 'BANK_SUBMIT' }

        it 'converts it into the PENDING status in the resulting request' do
          subject
          expect(a_request(:post, gds_base_url).with(body: {
                                                       call_type: 'syncStatus',
                                                       request_id:,
                                                       product_type:,
                                                       loan_app: { app_status: 'PENDING' }.as_json
                                                     })).to have_been_made
        end
      end

      describe 'with decsion_reason_number' do
        let(:status) { 'BACK_END_DECLINED' }
        let(:decision_reason_number) { rand(20) }

        it 'sends a request including the decision_reason_number' do
          subject
          expect(a_request(:post, gds_base_url).with(body: {
                                                       call_type: 'syncStatus',
                                                       request_id:,
                                                       product_type:,
                                                       loan_app: { app_status: 'BACK_END_DECLINED' }.as_json,
                                                       decision_reason_number:
                                                     })).to have_been_made
        end
      end

      describe 'with wrong status type' do
        let(:status) { 9 }

        it 'raises an error' do
          expect { subject }.to raise_error('status must be an instance of String')
        end
      end
    end

    describe '.get_tasks' do
      subject { described_class.get_tasks(request_id:, app_status:, product_type:) }
      it 'sends out a request' do
        subject
        expect(a_request(:post, gds_base_url).with(body: {
                                                     call_type: 'getTasks',
                                                     request_id:,
                                                     product_type:,
                                                     loan_app: { app_status: }
                                                   })).to have_been_made
      end
    end

    describe '.update_task_statuses' do
      let(:task_status) { build(:gds_api_task_status) }

      subject { described_class.update_task_statuses(request_id:, task_statuses: [task_status], product_type:) }

      it 'sends out a request' do
        subject
        expect(a_request(:post, gds_base_url).with(body: {
                                                     call_type: 'toDoStatusChanges',
                                                     request_id:,
                                                     product_type:,
                                                     tasks: [
                                                       {
                                                         id: task_status.id,
                                                         type: task_status.type,
                                                         status: task_status.status
                                                       }
                                                     ]
                                                   })).to have_been_made
      end
    end

    describe '.update_document_statuses' do
      let(:document_status) { build(:gds_api_document_status) }

      subject { described_class.update_document_statuses(request_id:, document_statuses: [document_status], product_type:) }

      it 'sends out a request' do
        subject
        expect(a_request(:post, gds_base_url).with(body: {
                                                     call_type: 'toDoStatusChanges',
                                                     request_id:,
                                                     product_type:,
                                                     documents: [
                                                       {
                                                         id: document_status.id,
                                                         status: document_status.status,
                                                         rejected_reason: document_status.rejected_reason
                                                       }
                                                     ]
                                                   })).to have_been_made
      end
    end

    describe '.get_verification_details' do
      subject { described_class.get_verification_details(request_id:) }
      it 'sends out a request' do
        subject
        expect(a_request(:post, gds_base_url).with(body: {
                                                     call_type: 'verificationRequirements',
                                                     request_id:
                                                   })).to have_been_made
      end
    end

    describe '.add_bank' do
      subject { described_class.add_bank(request_id:, bank_details:, loan: above_loan_app) }

      let(:loan_app_status) { LoanAppStatus.for((LoanAppStatus::ID_TO_NAME - %w[NONE BANK_SUBMIT]).sample) }

      before { above_loan_app.update!(loan_app_status:) }

      describe 'using proper data object' do
        it 'sends out a request' do
          subject
          expect(a_request(:post, gds_base_url)
            .with(
              body: {
                call_type: 'addBank',
                request_id:,
                loan_app: { app_status: above_loan_app.loan_app_status.name },
                product_type: above_loan_app.product_type,
                **bank_details.attributes
              }.as_json
            )).to have_been_made
        end
      end

      describe 'using wrong data object' do
        let(:bank_details) do
          {
            wrong: 'type'
          }
        end

        it 'raises an error' do
          expect { subject }.to raise_error("bank_details must be an instance of #{described_class::BankDetails}")
        end
      end
    end

    describe '.sync_docs' do
      subject { described_class.sync_docs(task_id:) }
      it 'sends out a request' do
        subject
        expect(a_request(:post, gds_base_url).with(body: {
                                                     call_type: 'syncDocs',
                                                     task_id:
                                                   })).to have_been_made
      end
    end

    describe '.submit_documents' do
      subject { described_class.submit_documents(request_id:, product_type:, task_id:, loan_app:, documents:) }

      describe 'with correct data objects' do
        it 'sends out a request' do
          subject
          expect(a_request(:post, gds_base_url).with(body: {
                                                       call_type: 'submitDocument',
                                                       request_id:,
                                                       product_type:,
                                                       task_id:,
                                                       loan_app: { app_status: },
                                                       documents: documents.map(&:attributes)
                                                     })).to have_been_made
        end
      end

      describe 'with wrong documents' do
        let(:documents) do
          [1, 2, 3]
        end

        it 'raises an error' do
          expect { subject }.to raise_error("documents must be an array containing instances of #{described_class::Document}")
        end
      end

      describe 'with wrong loan_app' do
        let(:loan_app) do
          {
            wrong: 'type'
          }
        end

        it 'raises an error' do
          expect { subject }.to raise_error("loan_app must be an instance of #{described_class::LoanApplication}")
        end
      end
    end

    describe '.save_selection' do
      let(:unified_id) { 'unique_unified_id' }

      subject { described_class.save_selection(request_id:, offer_id:, app_status:, product_type:, unified_id:) }

      it 'sends out a request' do
        subject
        expect(a_request(:post, gds_base_url).with(body: {
                                                     call_type: 'saveSelection',
                                                     request_id:,
                                                     offer_id:,
                                                     product_type:,
                                                     loan_app: { app_status:, unified_id: }
                                                   })).to have_been_made
      end
    end

    describe '.retrieve_credit_report' do
      subject { described_class.retrieve_credit_report(request_id:) }

      it 'sends out a request and parses the response' do
        subject
        expect(a_request(:post, gds_base_url).with(body: {
                                                     call_type: 'borrowerReports',
                                                     application_id: request_id,
                                                     report_type: 'informative_soft_pull'
                                                   })).to have_been_made
      end
    end

    describe 'retrieve_borrower_reports' do
      subject { described_class.retrieve_borrower_reports(request_id:) }

      let(:gds_response) do
        {
          reports: [
            {
              generated_at: '2025-01-15T20:18:14+0000',
              raw_report: '<raw sensitive report>',
              report_type: 'socure',
              success: true,
              error_message: ''
            },
            {
              generated_at: '2025-01-15T21:18:14+0000',
              raw_report: '<another raw report>',
              report_type: 'giact',
              success: true,
              error_message: ''
            }
          ]
        }
      end

      before { allow(Clients::GdsApi::BorrowerReportsParser).to receive(:from_reports_response).and_call_original }

      it 'sends an appropriate POST request to the GDS API' do
        subject

        expect(a_request(:post, gds_base_url).with(body: {
                                                     call_type: 'borrowerReports',
                                                     application_id: request_id
                                                   })).to have_been_made
      end

      it 'parses and returns the borrower reports correctly' do
        reports = subject

        expect(Clients::GdsApi::BorrowerReportsParser).to have_received(:from_reports_response).with(gds_response.as_json)

        expect(reports.length).to eq(gds_response[:reports].length)
        gds_response[:reports].each do |test_report|
          returned_report = reports.find { |report| report[:type] == test_report[:report_type] }
          expect(returned_report).to be_present
          expect(returned_report[:generated_at]).to be_within(1.second).of(test_report[:generated_at].to_datetime)
          expect(returned_report[:raw_report]).to eq(test_report[:raw_report])
          expect(returned_report[:valid]).to eq(true)
          expect(returned_report[:error_message]).to be_empty
        end
      end

      it 'logs the response body with raw_report replaced by [FILTERED]' do
        subject

        event = expect_api_event_record(name: 'gds_retrieve_borrower_reports')
        expect(event.metadata['call_type']).to eq('borrowerReports')
        expect(event.metadata['attempt']).to eq(1)
        expect(event.response).to eq({
                                       'reports' => [
                                         { 'error_message' => '',
                                           'generated_at' => '2025-01-15T20:18:14+0000',
                                           'raw_report' => '[FILTERED]',
                                           'report_type' => 'socure',
                                           'success' => true },
                                         { 'error_message' => '',
                                           'generated_at' => '2025-01-15T21:18:14+0000',
                                           'raw_report' => '[FILTERED]',
                                           'report_type' => 'giact',
                                           'success' => true }
                                       ]
                                     })
      end
    end
  end

  describe 'with error' do
    before do
      allow(Rails.cache).to receive(:exist?).with(cache_key).and_return(true)
      allow(Rails.cache).to receive(:read).with(cache_key).and_return(access_token)
    end

    it 'raises a generic error' do
      stub_request(:post, gds_base_url)
        .to_raise('Boom!')
      expect { described_class.new_loan_app(product_type: 'IPL', loan_app:, borrower:) }.to raise_error('Boom!')
    end

    it 'retries server errors and eventually reraises' do
      request = stub_request(:post, gds_base_url).to_return(status: 504)
      expect { described_class.new_loan_app(product_type: 'IPL', loan_app:, borrower:) }.to raise_error(Clients::GdsApi::Error) do |error|
        expect(error.wrapped_exception).to be_a(Faraday::ServerError)
        expect(error.message).to match(
          response_body: '',
          response_status: 504,
          klass: 'Faraday::ServerError',
          message: /the server responded with status 504/
        )
      end

      expect(request).to have_been_requested.times(3)
    end

    it 'retries connection errors and eventually reraises' do
      request = stub_request(:post, gds_base_url).to_raise(Faraday::ConnectionFailed)
      expect { described_class.new_loan_app(product_type: 'IPL', loan_app:, borrower:) }.to raise_error(Clients::GdsApi::Error) do |error|
        expect(error.wrapped_exception).to be_a(Faraday::ConnectionFailed)
        expect(error.message).to eq({ response_body: nil,
                                      response_status: nil,
                                      klass: 'Faraday::ConnectionFailed',
                                      message: 'Exception from WebMock' })
      end
      expect(request).to have_been_requested.times(3)
    end
  end
end
