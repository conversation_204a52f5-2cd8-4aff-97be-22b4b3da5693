# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Clients::Middleware::EnsureJson do
  let(:data) { { test: 'data', numbers: 123 }.stringify_keys }

  it 'parses valid JSoN response body' do
    stub_request(:get, 'http://example.com/test').to_return(
      status: 200,
      body: data.to_json,
      headers: {}
    )

    response = Faraday.new do |f|
      f.use described_class
    end.get('http://example.com/test')

    expect(response.env[:body]).to eq(data)
  end

  it 'returns the body when malformed json' do
    stub_request(:get, 'http://example.com/test').to_return(
      status: 200,
      body: 'not json at all',
      headers: {}
    )

    response = Faraday.new do |f|
      f.use described_class
      f.adapter Faraday.default_adapter
    end.get('http://example.com/test')

    expect(response.env[:body]).to eq('not json at all')
  end

  it 'returns the body when body is empty' do
    stub_request(:get, 'http://example.com/test').to_return(
      status: 200,
      body: '',
      headers: {}
    )

    response = Faraday.new do |f|
      f.use described_class
      f.adapter Faraday.default_adapter
    end.get('http://example.com/test')

    expect(response.env[:body]).to eq('')
  end

  it 'returns empty string when body is nil' do
    stub_request(:get, 'http://example.com/test').to_return(
      status: 200,
      body: nil,
      headers: {}
    )

    response = Faraday.new do |f|
      f.use described_class
      f.adapter Faraday.default_adapter
    end.get('http://example.com/test')

    expect(response.env[:body]).to be_blank
  end
end
