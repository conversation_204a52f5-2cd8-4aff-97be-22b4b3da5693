# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Mockit::Mock::OcrolusWebhooksController do
  describe '.mock_call' do
    let(:service_key) { 'api/ocrolus_webhooks_controller' }
    subject(:verifier) do
      Class.new(Api::OcrolusWebhooksController) do
        def verify_authenticity!
          'called'
        end
      end
    end
    before do
      allow(subject).to receive(:name).and_return('Api::OcrolusWebhooksController')
      Mockit.mock_classes(subject => Mockit::Mock::OcrolusWebhooksController)
    end

    after do
      RequestStore.clear!
    end

    it 'returns true for mocked response' do
      Mockit::Store.mock_id = 1
      Mockit::Store.write(service: service_key, overrides: {})

      expect(subject.new.verify_authenticity!).to eq(nil)
    end

    it 'calls original without mock set' do
      expect(subject.new.verify_authenticity!).to eq('called')
    end
  end
end
