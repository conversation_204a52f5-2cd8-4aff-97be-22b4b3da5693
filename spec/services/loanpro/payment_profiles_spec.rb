# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Loanpro::PaymentProfiles do
  let(:loanpro_loan_id) { '7849' }
  let(:customers_response) { { 'Customers' => [{ 'id' => 66_933 }] } }
  let(:payment_profile_response) do
    {
      'results' => [
        {
          'CheckingAccount' => {
            'accountType' => 'bankacct.type.checking',
            'accountNumber' => '************',
            'routingNumber' => '*********',
            'bankName' => 'Wells Fargo',
            'isPrimary' => 1,
            'isSecondary' => 0,
            'title' => 'Personal Account ********',
            'type' => 'paymentAccount.type.checking',
            'active' => 1,
            'visible' => 1
          }
        }
      ]
    }
  end

  before do
    allow(Clients::LoanproApi).to receive(:get_loan).and_return(customers_response)
    allow(Clients::LoanproApi).to receive(:fetch_primary_payment_profile).and_return(payment_profile_response)
    allow(Servicing::PaymentProfiles).to receive(:new).and_call_original
  end

  subject { described_class.new(loanpro_loan_id:) }

  describe '#call' do
    context 'when a loan has payment profile' do
      it 'calls the LoanPro API to fetch customer id and payments profile' do
        subject.call

        expect(Clients::LoanproApi).to have_received(:get_loan)
        expect(Clients::LoanproApi).to have_received(:fetch_primary_payment_profile)
      end

      it 'calls PaymentProfiles to constuct an array of PaymentProfile objects' do
        subject.call

        expect(Servicing::PaymentProfiles).to have_received(:new)
      end

      it 'returns a PaymentProfile object' do
        payment_object = subject.call
        expect(payment_object.payment_profiles[0].bank_name).to eq('Wells Fargo')
      end
    end

    context 'when call to loanpro api returns a error' do
      before do
        allow(Clients::LoanproApi).to receive(:get_loan).and_raise(Faraday::Error.new('Connection error'))
        allow(Rails.logger).to receive(:error).with('Error in PaymentProfile', hash_including(error_message: 'Connection error'))
      end
      it 'logs and raises an error' do
        expect(Rails.logger).to receive(:error).with('Error in PaymentProfile', hash_including(error_message: 'Connection error'))
        expect { subject.call }.to raise_error(Loanpro::PaymentProfiles::PaymentProfileSystemError)
      end
    end

    context 'when checking account is missing' do
      let(:payment_profile_response) { { 'results' => [{ 'id' => 1 }] } }

      before do
        allow(Rails.logger).to receive(:info)
      end

      it 'logs an error' do
        subject.call
        expect(Rails.logger).to have_received(:info).with('Missing CheckingAccount for Payment Profile ID: 1')
      end
    end
  end
end
