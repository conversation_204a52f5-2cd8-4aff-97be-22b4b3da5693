# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Loanpro::UpcomingPayments do
  let(:loan_id) { '12345' }
  let(:subject) { described_class.new(loan_id: loan_id) }
  let(:api_response) do
    {
      Autopays: [
        {
          id: 1,
          amount: 111.12,
          applyDate: "/Date(#{'2022-01-11'.to_datetime.to_i})/",
          type: 'autopay.type.single',
          status: 'autopay.status.pending'
        },
        {
          id: 2,
          amount: 200.13,
          applyDate: "/Date(#{'2022-01-01'.to_datetime.to_i})/",
          type: 'autopay.type.single',
          status: 'autopay.status.completed'
        },
        {
          id: 3,
          amount: 200.13,
          applyDate: "/Date(#{'2022-01-01'.to_datetime.to_i})/",
          type: 'autopay.type.single',
          status: 'autopay.status.cancelled'
        },
        {
          id: 4,
          amount: 200.13,
          applyDate: "/Date(#{'2022-01-01'.to_datetime.to_i})/",
          type: 'autopay.type.single',
          status: 'autopay.status.failed'
        }
      ]
    }.deep_stringify_keys
  end

  before do
    allow(Clients::LoanproApi).to receive(:get_loan).and_return(api_response)
  end

  describe '#call' do
    context 'when the loan has autopays' do
      it 'returns the upcoming payments' do
        result = subject.call
        expect(result.payments.count).to eq(1) # Assuming there's only one upcoming payment in the response
        expect(result.payments.first.id).to eq(1)
        expect(result.payments.first.amount).to eq(111.12)
        expect(result.payments.first.date).to eq(LoanproHelper.parse_date("/Date(#{'2022-01-11'.to_datetime.to_i})/").iso8601)
        expect(result.payments.first.type).to eq('single')
        expect(result.payments.first.status).to eq('pending')
      end
    end

    context 'when the loan has no autopays' do
      before do
        allow(Clients::LoanproApi).to receive(:get_loan).and_return({})
      end
      it 'returns an empty array' do
        result = subject.call
        expect(result.payments.count).to eq(0)
      end
    end

    context 'when loanpro throws an error' do
      it 'raises an error when loanpro throws an error' do
        allow(Clients::LoanproApi).to receive(:get_loan).and_raise(StandardError, 'Loanpro API error')
        expect { subject.call }.to raise_error(Loanpro::UpcomingPayments::UpcomingPaymentsSystemError, 'upcoming payments error - Loanpro API error')
      end
    end
  end
end
