# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Documents::GenerateUplInstallmentLoanAgreementPdf do
  include ActiveSupport::Testing::TimeHelpers

  subject(:service) do
    described_class.new(loan:, til_history:, loanpro_loan_data:)
  end

  let(:borrower) { create(:borrower, first_name: '<PERSON>', last_name: '<PERSON><PERSON>') }
  let(:bypass_beyond_status_update) { true }
  let(:loan) { create(:upl_loan, borrower:, originating_party: Loan::ORIGINATING_PARTIES[:CRB], amount: 22_497.97) }
  let(:til_data) do
    # This is a partial TIL data payload to prevent bloating this test unnecessarily.
    {
      loan: {
        apr: '26.93%',
        amount: '$46,396.10',
        unified_id: '********',
        contractDate: '12/09/2023',
        maturityDate: '12/08/2027',
        product_type: 'UPL',
        agreementDate: '12/07/2023',
        principalLoanAmount: '$48,838.00'
      },
      borrower: {
        city: 'Brakushaven',
        state: 'IL',
        zip_code: '94025',
        last_name: 'LAMBERT',
        first_name: 'ERIC<PERSON>',
        address_street: '409 GLENWOOD'
      },
      bankAccount: {
        bank: 'Wells Fargo',
        account_type: 'checking',
        account_number: '************',
        routing_number: '*********',
        fund_transfer_authorize: true
      },
      itemization: {
        amountFinanced: '$46,396.10',
        rawAmountFinanced: '46396.10',
        principalLoanAmount: '$48,838.00',
        prepaidFinanceCharge: '$2,441.90'
      },
      payment_schedule: [
        {
          due: 'Monthly payments starting on 01/08/2024',
          amount: '$1,588.63',
          number: '48',
          rawAmount: 1588.63,
          rawNumber: 48,
          rawDueDate: '01/08/2024'
        }
      ],
      additionalLoanAgreementData: {
        frequency: 'Monthly',
        contractDate: '2023-12-09',
        interestRate: '23.841',
        maturityDate: '12/08/2027',
        underwriting: '2441.90',
        principalLoanAmount: '48838.00'
      }
    }.with_indifferent_access
  end
  let(:til_history) { create(:til_history, loan:, til_data:) }
  let(:loanpro_loan_data) { { LoanSetup: { underwriting: 123.4567, origFinalPaymentDate: '/Date(**********)/' } }.with_indifferent_access }

  let(:template_type) { DocTemplate::TYPES[:DM_CRB_INSTALLMENT_LOAN_AGREEMENT] }
  let(:template_body) { 'Test Document Body. Signature: /docusign_sign/' }
  let!(:template) { create(:doc_template, type: template_type, body: template_body) }

  let(:rendered_template) { 'Rendered Template Content' }
  let(:pdf_content) { "%PDF-1.4\n..." }

  before do
    allow(Documents::RenderTemplateAsHtml).to receive(:call).and_return(rendered_template)
    allow(Documents::GeneratePdfFromHtml).to receive(:call).and_return(pdf_content)

    next unless loan

    create(:borrower_additional_info, borrower:, address_street: '123 Main St', city: 'Chicago', state: 'IL', zip_code: '60606')
    create(:offer, loan:, selected: true, interest_rate: 23.45678)
    create(:bank_account, loan:, borrower:, account_number: '*************', routing_number: '*********', fund_transfer_authorize: [true, false].sample)
  end

  context 'when no loan is specified' do
    let(:loan) { nil }

    it 'raises an error' do
      expect { service.call }.to raise_error(ActiveRecord::RecordInvalid, /Loan must exist/i)
    end
  end

  context 'when no TIL history is specified' do
    let(:til_history) { nil }

    it 'raises an error' do
      expect { service.call }.to raise_error(ActiveModel::ValidationError, /Til history is required/i)
    end
  end

  context 'when no LoanPro loan is specified' do
    let(:loanpro_loan_data) { nil }

    it 'raises an error' do
      expect { service.call }.to raise_error(ActiveModel::ValidationError, /Loanpro loan data is required/i)
    end
  end

  context 'when the loan does not have a selected_offer' do
    before do
      loan.selected_offer.update!(selected: false)
    end

    it 'raises an error' do
      expect { service.call }.to raise_error(ActiveModel::ValidationError, /loan does not have a selected_offer/i)
    end
  end

  it 'raises an error if the template does not contain a signature field' do
    template.update!(body: 'Test Document Body.')
    expect { service.call }.to raise_error(ActiveModel::ValidationError, /No DocuSign sign field found in document template #{template.id}/i)
  end

  it 'raises an error if no installment loan agreement template exists' do
    template.destroy!
    expect { service.call }.to raise_error(ActiveModel::ValidationError, /Template is required/i)
  end

  it 'renders an instance of the installment loan agreement template with the correct variables' do
    travel_to('2023-08-15T12:00:00Z'.to_time)
    expected_variables = {
      loan_number: '********',
      loan_date: '12/07/2023',
      maturity_date: '12/08/2027',
      frequency: 'Monthly',
      funding_date: '12/09/2023',
      offer_interest_rate: '23.84%',
      underwriting: '$2,441.90',
      principal_loan_amount: '$48,838.00',
      loan: til_data['loan'],
      customer_name: 'ERICA LAMBERT',
      customer_address: '409 GLENWOOD',
      customer_city_state_zip: 'Brakushaven, IL 94025',
      state: 'IL',
      account_number: '************',
      routing_number: '*********',
      fund_transfer_authorize: true,
      paymentSchedule: til_data['payment_schedule'],
      first_payment_due_date: '01/08/2024',
      itemization: til_data['itemization'].merge(cashoutAmount: ''),
      certificate_of_registration: '___________',
      documentary_stamp_tax: '$ ___________',
      state_initials_ne: ''
    }
    service.call
    expect(Documents::RenderTemplateAsHtml).to have_received(:call).with(template:, variables: expected_variables)
  end

  context 'when the borrower is from Florida' do
    before { til_data['borrower']['state'] = 'FL' }

    it 'populates the certificate_of_registration template variable properly' do
      service.call
      expect(Documents::RenderTemplateAsHtml).to have_received(:call).with(template:, variables: hash_including(certificate_of_registration: '78-**********-0'))
    end

    it 'populates the documentary_stamp_tax template variable properly' do
      service.call
      expect(Documents::RenderTemplateAsHtml).to have_received(:call).with(template:, variables: hash_including(documentary_stamp_tax: '$171.15'))
    end
  end
  context 'when the borrower is from Nebraska' do
    before { til_data['borrower']['state'] = 'NE' }

    it 'populates the state_initials_ne template variable properly' do
      service.call
      expect(Documents::RenderTemplateAsHtml).to have_received(:call).with(template:, variables: hash_including(state_initials_ne: 'E L'))
    end
  end
  context 'when the borrower is from Wisconsin' do
    before { til_data['borrower']['state'] = 'WI' }

    it 'populates the state_signature_wi template variable properly' do
      service.call
      expect(Documents::RenderTemplateAsHtml).to have_received(:call).with(template:, variables: hash_including(state_signature_wi: true))
    end
  end

  it 'generates a PDF of the resulting installment loan agreement document and returns it with the template' do
    service.call
    expect(Documents::GeneratePdfFromHtml).to have_received(:call).with(html: rendered_template, document_label: template.name, custom_pdf_options: { margin: { left: 40, right: 40 } })
  end

  it 'returns a ContractDocument record for the resulting PDF document' do
    contract_document = service.call
    expect(contract_document).to be_a(Documents::ContractDocument)
    expect(contract_document.template_type).to eq(DocTemplate::TYPES[:DM_CRB_INSTALLMENT_LOAN_AGREEMENT])
    expect(contract_document.content).to eq(pdf_content)
    expect(contract_document.filename).to match(/^#{template.type}_version_#{template.version}_#{borrower.first_name}_#{borrower.last_name}_[0-9a-f-]{36}$/i)
    expect(contract_document.template).to eq(template)
  end
end
