# frozen_string_literal: true

require 'rails_helper'

RSpec.describe SignedLoanproLoan::Lookup do
  let(:loan) { build(:loan) }
  let(:unified_id) { loan.unified_id }
  let(:loanpro_loan_id) { SecureRandom.uuid }

  let(:loanpro_result) do
    {
      'results' => [
        { 'id' => loanpro_loan_id, 'active' => 1 }
      ]
    }
  end

  let(:multi_loanpro_results) do
    {
      'results' => [
        { 'id' => loanpro_loan_id, 'active' => 1 },
        { 'id' => SecureRandom.uuid, 'active' => 1 }
      ]
    }
  end

  let(:empty_loanpro_results) do
    { 'results' => [] }
  end

  let(:subject) { described_class.call(loan:) }

  def build_expected_results(loanpro_result)
    {
      results: loanpro_result['results'].map do |loan|
        { id: loan['id'] }
      end,
      total: loanpro_result['results'].count
    }
  end

  before do
    allow(Clients::LoanproApi)
      .to receive(:search_active_loanpro_loan_by)
      .and_return(empty_loanpro_results)

    allow(Clients::LoanproApi)
      .to receive(:get_active_loanpro_loan_by)
      .and_return(empty_loanpro_results)
  end

  context '#call' do
    context 'when getting active loanpro loans from search api' do
      before do
        expect(Clients::LoanproApi)
          .to receive(:search_active_loanpro_loan_by)
          .with(unified_id:)
          .and_return(loanpro_result)
      end

      it 'gets active loanpro loans from search api' do
        expect(subject).to eq build_expected_results(loanpro_result)
        expect(subject[:results].first[:id]).to eq(loanpro_loan_id)
        expect(subject[:total]).to eq(1)
      end
    end

    context 'when active loanpro loans have multiple results' do
      before do
        expect(Clients::LoanproApi)
          .to receive(:search_active_loanpro_loan_by)
          .with(unified_id:)
          .and_return(multi_loanpro_results)
      end

      it 'gets active loanpro loans from search api' do
        expect(subject[:total]).to eq(2)
      end
    end

    context 'when calling loans api when no search results' do
      before do
        expect(Clients::LoanproApi)
          .to receive(:get_active_loanpro_loan_by)
          .with(unified_id:)
          .and_return(loanpro_result)
      end

      it 'calls loans api when no search results' do
        expect(subject).to eq build_expected_results(loanpro_result)
      end
    end

    context 'when calling loans api when search times out' do
      before do
        expect(Clients::LoanproApi)
          .to receive(:search_active_loanpro_loan_by)
          .and_raise(Clients::LoanproApi::TimeoutError.new('timed out'))

        expect(Clients::LoanproApi)
          .to receive(:get_active_loanpro_loan_by)
          .with(unified_id:)
          .and_return(loanpro_result)
      end

      it 'calls loans api when search times out' do
        expect(subject).to eq build_expected_results(loanpro_result)
      end
    end

    context 'when calling loans api when it times out' do
      before do
        expect(Clients::LoanproApi)
          .to receive(:get_active_loanpro_loan_by)
          .and_raise(Clients::LoanproApi::TimeoutError.new('timed out'))
      end

      it 'calls loans api when get times out' do
        expect(subject).to eq build_expected_results(empty_loanpro_results)
      end
    end

    context 'when both APIs return no results' do
      it 'returns empty result set' do
        expect(subject).to eq build_expected_results(empty_loanpro_results)
      end
    end
  end
end
