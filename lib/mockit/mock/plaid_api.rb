# frozen_string_literal: true

module Mockit
  module Mock
    module <PERSON>laid<PERSON><PERSON>
      def self.mock_get_asset_report(overrides, super_method, _token)
        if overrides['get_asset_report']
          json = JSON.parse(overrides['get_asset_report'])
          response = GenericResponse.new(json)
          Clients::PlaidApi::AssetReportResults.new(
            response:,
            asset_report_id: SecureRandom.hex(10)
          )
        else
          super_method.call
        end
      end

      def self.mock_create_asset_report(_overrides, _super_method, *_args, **_kwargs)
        response = GenericResponse.new('{"status": 200, "body": "Mock response"}')
        Clients::PlaidApi::AssetReportCreationResults.new(
          response: response,
          asset_report_id: Mockit::Store.current_mock_id,
          asset_report_token: Mockit::Store.current_mock_id
        )
      end

      def self.mock_create_link_token(_overrides, _super_method, *_args, **_kwargs)
        response = GenericResponse.new('{"status": 200, "body": "Mock response"}')
        Clients::PlaidApi::LinkToken.new(
          response: response,
          token: Mockit::Store.current_mock_id
        )
      end

      def self.mock_exchange_public_token(_overrides, _super_method, *_args, **_kwargs)
        response = GenericResponse.new('{"status": 200, "body": "Mock response"}')
        Clients::PlaidApi::ApiToken.new(
          response: response,
          access_token: Mockit::Store.current_mock_id,
          item_id: SecureRandom.hex(10)
        )
      end

      def self.mock_get_institution(_overrides, _super_method, *_args, **_kwargs)
        Clients::PlaidApi::Institution.new(
          response: GenericResponse.new(''),
          id: SecureRandom.hex(10),
          name: 'Wells Fargo'
        )
      end

      def self.mock_get_auth_data(overrides, _super_method, *_args, **_kwargs)
        accounts = (overrides['auth_data'] && overrides['auth_data']['accounts']) || [{}]

        account_number = '*********'
        last4 = account_number[-4..]
        routing = '*********'
        inst = SecureRandom.hex(10)

        defaults = {
          plaid_id: Mockit::Store.current_mock_id, account_number: account_number, last_four_account_number: last4,
          routing_number: routing, name: 'First Mock Checking account', type: 'checking', institution_id: inst
        }
        bank_accounts = accounts.map do |acc|
          Clients::PlaidApi::BankAccount.new(**defaults.merge(acc))
        end
        Clients::PlaidApi::AuthResults.new(response: GenericResponse.new('auth_data'), accounts: bank_accounts)
      end

      def self.mock_get_identity_data(overrides, _super_method, *_args, **_kwargs)
        owners = overrides.dig('identity_data', 'account_owners') || [{}]
        data = {
          plaid_id: Mockit::Store.current_mock_id,
          first_name: 'Mock',
          last_name: 'Mockovich'
        }

        acc_owners = owners.map do |owner|
          Clients::PlaidApi::AccountOwner.new(**data.merge(owner))
        end
        Clients::PlaidApi::IdentityResults.new(
          response: GenericResponse.new('identity'),
          account_owners: acc_owners
        )
      end
    end
  end
end
