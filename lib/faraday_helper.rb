# frozen_string_literal: true

module FaradayHelper
  # Helper method for setting up the middleware stack on a Faraday connection. Provides
  # some customizability with `json` and `raise_error` options. CFRayForwarder, logger,
  # and ResponseTimer middlewares are always included.
  #
  # Example usage:
  #
  # Faraday.new(url: base_url) do |f|
  #   FaradayHelper.middleware(f)
  # end
  #
  # Faraday.new(url: base_url) do |f|
  #   f.request(:url_encoded) # we can add other middleware before (or after) the common ones
  #   FaradayHelper.middleware(f, json: false)
  # end
  #
  def self.middleware(connection, json: true, force_json: false, raise_error: true)
    connection.use(Clients::Middleware::CfRayForwarder)
    connection.request(:json) if json
    connection.response(:raise_error) if raise_error
    connection.response(:json) if json
    connection.use(Clients::Middleware::EnsureJson) if force_json
    connection.response(:logger, Rails.logger, headers: false)
    connection.use(Clients::Middleware::ResponseTimer) # positioned last to put the timing closer to the actual request
  end
end
