# frozen_string_literal: true

module Sidekiq
  module TaggedLogging
    TAGGED_LOGGING = 'tag_logging'

    class Client
      include Sidekiq::ClientMiddleware

      # job the full job payload
      # * @see https://github.com/sidekiq/sidekiq/wiki/Job-Format
      #
      # yield the next middleware in the chain or the enqueuing of the job
      def call(_, job, _, _)
        job[TAGGED_LOGGING] = ObservabilityAttributes.attributes
        yield
      end
    end

    class Server
      include Sidekiq::ServerMiddleware

      def call(_, job, _, &)
        observability_attrs = job[TAGGED_LOGGING] || default_attributes
        ObservabilityAttributes.reset_attributes!(observability_attrs)
        yield
      end

      private

      # Adding request_id for Sidekiq jobs to track them similarly to HTTP requests.
      # The cf_ray ID provides additional tracking for Faraday requests to other services.
      def default_attributes
        {
          request_id: SecureRandom.uuid,
          cf_ray: SecureRandom.uuid
        }
      end
    end
  end
end
