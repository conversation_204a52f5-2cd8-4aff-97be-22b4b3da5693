# frozen_string_literal: true

module Clients
  class DashServicingApi
    class Error < Clients::Errors::Faraday; end

    EXTERNAL_SERVICING_CODE = 'servicing_dashboard'

    class << self
      def beneficiary_owner_details(borrower:, unified_id:)
        request_body = {}
        @borrower = borrower
        response = connection.get("servicing/loan/#{unified_id}/beneficial_owner")
        result = response.body
        record_response(request_body:, response:, meta:)
        result
      rescue Faraday::Error => e
        record_error(request_body:, exception: e, meta:)
        raise Error.new(e, e.response)
      end

      private

      attr_reader :borrower

      def meta
        { borrower_id: borrower.id }
      end

      def record_response(request_body:, response:, meta: {})
        # NOTE:  Pulls calling location from stack
        # https://www.rubydoc.info/stdlib/core/2.0.0/Kernel%3Acaller_locations
        event_name = "dash_servicing_#{caller_locations(1, 1).first.base_label}"
        RecordApiEvent.call(event_name:, request_body:, response:, meta:)
      end

      def record_error(request_body:, exception:, meta: {})
        # NOTE:  Pulls calling location from stack
        # https://www.rubydoc.info/stdlib/core/2.0.0/Kernel%3Acaller_locations
        event_name = "dash_servicing_#{caller_locations(1, 1).first.base_label}"

        RecordApiEvent.call(event_name:, request_body:, response: exception.response, meta:)
      end

      # Generate Oauth Token for Servicing Dashboard.
      def servicing_dashboard_token
        Rails.cache.fetch('servicing_dashboard_token', expires_in: 1.day) do
          app = ExternalApp.find_by(code: EXTERNAL_SERVICING_CODE)
          Auth::GenerateExternalAppToken.call(client_id: app.client_id, client_secret: app.client_secret,
                                              grant_type: 'client_credentials')
        end
      end

      def connection
        Faraday.new(url: config[:base_url], headers:) do |f|
          FaradayHelper.middleware(f)
        end
      end

      def headers
        {
          'Content-Type': 'application/json',
          'X-Authorization': borrower_token,
          Authorization: "Bearer #{servicing_dashboard_token}"
        }
      end

      def borrower_token
        Auth::GenerateBorrowerTokens.call(borrower:)[:access_token]
      end

      def config
        Rails.application.config_for(:dash_api)
      end
    end
  end
end
