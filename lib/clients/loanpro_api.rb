# frozen_string_literal: true

# A client facade for interacting with the [LoanPro Loan Management System API](https://developers.loanpro.io/reference)
# rubocop:disable Metrics/ClassLength
module Clients
  class LoanproApi < LoanproApiBase
    PAID_OFF_LOAN_STATUS_ID = 6
    SEARCH_TIMEOUT = 30

    class Error < Clients::Errors::Faraday; end
    class TimeoutError < Error; end

    class << self
      def create_temporary_loan(offer, contract_date, first_payment_date, payment_frequency, unified_id)
        request_body =
          create_temporary_loan_body(offer, contract_date, first_payment_date, payment_frequency, unified_id)
        response = connection.post('odata.svc/Loans', request_body.to_json)

        handle_success(request_body:, response:)
      rescue Faraday::Error => e
        handle_error(request_body:, exception: e)
      end

      # This method is only exposed publicly to support the requirement of our third party request tracking to
      # track this payload as the inputs to the temporary loan creation request. Once third party request tracking
      # is no longer necessary, this method should be made private.
      def create_temporary_loan_body(offer, contract_date, first_payment_date, payment_frequency, unified_id) # rubocop:disable Metrics/MethodLength
        body = {
          temporaryAccount: 1,
          LoanSetup: {
            loanAmount: format('%.2f', offer.amount_financed),
            loanRate: format('%.3f', offer.interest_rate),
            loanRateType: 'loan.rateType.annually',
            underwriting: format('%.2f', offer.origination_fee),
            loanTerm: offer.term,
            contractDate: contract_date,
            firstPaymentDate: first_payment_date,
            calcType: 'loan.calcType.simpleInterest',
            loanClass: 'loan.class.consumer',
            loanType: 'loan.type.installment',
            paymentFrequency: payment_frequency,

            # Copied this comment over from service-layer
            # https://github.com/Above-Lending/service-layer/blob/48f0ef5638d2f3bb26ec622f8c807460ad55b27f/services/loanProService.js#L390
            # We have an issue currently where some of our Loans produce a
            # payment schedule in LoanPro with three separate payment amounts.
            # The template agreed with CRB calls for two payment amounts.
            # LoanPro have let us know that if we set the discount value in our
            # API request to them to 0, when the loan is being onboarded, this
            # will ensure that the payment schedule has two payment amounts.

            discount: 0,
            discountSplit: 1,
            discountCalc: 'loan.discountCalc.straightLine'
          },
          loanIdOption: 'loan.idOption.custom',
          displayId: unified_id,
          # LoanPro raises an error for duplicate displayId's, so we purposely
          # ignoreWarnings to create these records in our system.
          __ignoreWarnings: true
        }

        apply_ignore_warnings(body)
      end

      # https://loanpro.readme.io/reference/activate-a-loan
      def activate_loan(loanpro_id)
        meta = request_body = { loanpro_id: }

        response = connection.post("Loans(#{loanpro_id})/AutoPal.Activate()")
        handle_success(request_body:, response:, meta:)
      rescue ::StandardError => e
        handle_error(request_body:, exception: e, meta:)
      end

      # https://developers.loanpro.io/reference/update-loan-custom-field-values
      def update_loan_settings_custom_fields(loanpro_id:, loan_settings_id:, custom_fields:)
        meta = { loanpro_id:, loan_settings_id: }
        request_body = {
          LoanSettings: {
            __id: loan_settings_id,
            __update: true,
            CustomFieldValues: { results: custom_fields.body }
          }
        }
        response = connection.put("odata.svc/Loans(#{loanpro_id})", request_body.to_json)

        handle_success(request_body:, response:, meta:)
      rescue ::StandardError => e
        handle_error(request_body:, exception: e, meta:)
      end

      # https://help.loanpro.io/article/t6tnagia14-api-activate-a-loan#inactivate_loan
      def deactivate_loan(loanpro_id, loanpro_setup_id)
        meta = { loanpro_id:, loanpro_setup_id: }
        request_body = {
          LoanSetup: {
            id: loanpro_setup_id,
            active: 0,
            __id: loanpro_setup_id,
            __update: true
          }
        }

        response = connection.put("odata.svc/Loans(#{loanpro_id})", request_body.to_json)

        handle_success(request_body:, response:, meta:)
      rescue ::StandardError => e
        handle_error(request_body:, exception: e, meta:)
      end

      def fetch_loan_details(loanpro_id, expands = ['LoanSetup'])
        meta = request_body = { loanpro_id: }
        response = connection.get("odata.svc/Loans(#{loanpro_id})?$expand=#{expands.join(',')}&nopaging")

        handle_success(request_body:, response:, meta:)
      rescue ::StandardError => e
        handle_error(request_body:, exception: e, meta:)
      end

      def fetch_primary_payment_profile(loanpro_customer_id, expands = [])
        meta = request_body = { loanpro_customer_id: }
        request_path = "odata.svc/Customers(#{loanpro_customer_id})/PaymentAccounts?all&$filter=isPrimary eq 1"
        request_path += "&$expand=#{expands.join(',')}&nopaging" if expands.present?

        response = connection.get(request_path)
        handle_success(request_body:, response:, meta:)
      rescue ::StandardError => e
        handle_error(request_body:, exception: e, meta:)
      end

      def create_customer(new_customer)
        meta = { unified_id: new_customer.unified_id }
        request_body = create_customer_body(new_customer)

        response = connection.post('odata.svc/Customers', request_body.to_json)

        handle_success(request_body:, response:, meta:)
      rescue ::StandardError => e
        handle_error(request_body:, exception: e, meta:)
      end

      def update_finalized_loan_body(update_params)
        link_loanpro_customer_body = link_customer_to_loan_body(update_params[:loanpro_loan_external_id],
                                                                update_params[:loanpro_customer_external_id])
        max_interest_body = assign_max_interest_body(update_params[:max_interest],
                                                     update_params[:loan_setup_id])
        portfolios_body = assign_portfolios_body(update_params[:portfolio_ids], update_params[:subportfolio_ids])
        link_loanpro_customer_body.merge!(max_interest_body, portfolios_body, { __temporary: false })
      end

      def update_finalized_loan(update_params)
        meta = update_params.slice(:loanpro_loan_external_id, :loan_setup_id)
        request_body = update_finalized_loan_body(update_params)

        response = connection.put("odata.svc/Loans(#{update_params[:loanpro_loan_external_id]})", request_body.to_json)

        handle_success(request_body:, response:, meta:)
      rescue ::StandardError => e
        handle_error(request_body:, exception: e, meta:)
      end

      def link_customer_to_loan_body(loanpro_loan_id, loanpro_customer_id)
        {
          id: loanpro_loan_id,
          Customers: {
            results: [
              {
                __id: loanpro_customer_id,
                __setLoanRole: 'loan.customerRole.primary'
              }
            ]
          },
          __update: true,
          __id: loanpro_loan_id
        }
      end

      def assign_max_interest_body(max_interest_amount, loanpro_loan_setup_id)
        {
          LoanSetup: {
            maxInterestAmount: max_interest_amount.to_f,
            __id: loanpro_loan_setup_id,
            __update: true
          }
        }
      end

      def assign_portfolios_body(portfolio_ids, subportfolio_ids)
        {
          Portfolios: { results: portfolio_ids.map { |id| { __id: id } } },
          SubPortfolios: { results: subportfolio_ids.map { |id| { __id: id } } }
        }
      end

      def create_autopay_body(new_autopay) # rubocop:disable Metrics/MethodLength
        {
          Autopays: {
            results: [
              {
                name: 'Scheduled Payment',
                type: 'autopay.type.recurring',
                paymentExtraTowards: 'payment.extra.tx.classicv1',
                amountType: 'autopay.amountType.static',
                amount: new_autopay.payment_amount,
                # Note the casing difference from the `PaymentType` attribute below. This mimics the current behavior of
                # Service Layer.
                # https://github.com/Above-Lending/service-layer/blob/944900087f319a1d31a416f4b80d65721384d69e/clients/loanPro.js#L580
                paymentType: 1,
                chargeServiceFee: '0',
                processCurrent: 1,
                retryDays: 0,
                processTime: 19,
                postPaymentUpdate: 1,
                applyDate: new_autopay.first_payment_date,
                processDate: new_autopay.process_date,
                methodType: 'autopay.methodType.echeck',
                recurringFrequency: new_autopay.recurring_frequency,
                recurringDateOption: 'autopay.recurringDate.applyDate',
                daysInPeriod: '',
                schedulingType: 'autopay.schedulingType.bankingDayPrior',
                processDateCondition: 'bankingDays',
                payoffAdjustment: 1,
                chargeOffRecovery: 0,
                paymentMethodAuthType: new_autopay.payment_method_auth_type,
                paymentMethodAccountType: new_autopay.payment_method_account_type,
                processZeroOrNegativeBalance: 0,
                applyLastDayOfMonthEnabled: new_autopay.last_day_of_month_enabled ? 1 : 0,
                PrimaryPaymentMethod: {
                  __metadata: {
                    uri: "http://loanpro.simnang.com/api/public/api/1/odata.svc/PaymentAccounts(id=#{new_autopay.payment_profile_id})",
                    type: 'Entity.PaymentAccount'
                  },
                  id: new_autopay.payment_profile_id,
                  type: 'paymentAccount.type.checking'
                },
                # We add a buffer, increasing the number of payments to be processed, to gracefully account for any
                # hardship options exercised on the loan. We are relying on LoanPro's built in functionality to
                # prevent payments from exceeding the account balance to avoid overcharging the client.
                # https://abovelending.slack.com/archives/C058QJJA0TY/p1696266531791019?thread_ts=**********.250519&cid=C058QJJA0TY
                # https://loanpro.helpjuice.com/en_US/294175-rms0532wth/AutoPay%20Defaults
                recurringPeriods: new_autopay.number_of_payments + 5,
                baProcessor: new_autopay.ba_processor,
                processDateTime: "#{new_autopay.process_date} 19:00:00",
                # Note the casing difference from the `paymentType` attribute above. This mimics the current behavior of
                # Service Layer.
                # https://github.com/Above-Lending/service-layer/blob/944900087f319a1d31a416f4b80d65721384d69e/clients/loanPro.js#L615
                PaymentType: {
                  __metadata: {
                    type: 'Entity.CustomPaymentType',
                    uri: '/api/1/odata.svc/CustomPaymentTypes(id=1)'
                  }
                }
              }
            ]
          }
        }
      end

      # https://developers.loanpro.io/reference/checkuncheck-checklist
      def update_checklist(loanpro_loan_id:, item_id:, value:)
        meta = { loanpro_loan_id:, item_id:, value: }
        request_body = {
          ChecklistItemValues: {
            results: [
              {
                checklistItemId: item_id,
                checklistItemValue: value,
                statusId: nil
              }
            ]
          }
        }
        response = connection.put("odata.svc/Loans(#{loanpro_loan_id})", request_body.to_json)
        handle_success(request_body:, response:, meta:)
      rescue ::StandardError => e
        handle_error(request_body:, exception: e, meta:)
      end

      def create_cip_autopay(loanpro_loan_external_id, new_autopay)
        meta = { loanpro_loan_external_id:, new_autopay: }
        request_body = {
          'Autopays' => {
            'results' => [
              new_autopay
            ]
          }
        }
        response = connection.put("odata.svc/Loans(#{loanpro_loan_external_id})", request_body)

        handle_success(request_body:, response:, meta:)
      rescue Faraday::TimeoutError => e
        raise TimeoutError.new(e, e.response)
      rescue ::StandardError => e
        handle_error(request_body:, exception: e, meta:)
      end

      def create_autopay(loanpro_loan_external_id, new_autopay)
        meta = { loanpro_loan_external_id:, new_autopay: }
        request_body = create_autopay_body(new_autopay)

        response = connection.put("odata.svc/Loans(#{loanpro_loan_external_id})", request_body.to_json)

        handle_success(request_body:, response:, meta:)
      rescue Faraday::TimeoutError => e
        raise TimeoutError.new(e, e.response)
      rescue ::StandardError => e
        handle_error(request_body:, exception: e, meta:)
      end

      def get_payments(loanpro_loan_id)
        response = connection.get("odata.svc/Loans(#{loanpro_loan_id})/Payments?nopaging=true")
        handle_success(request_body: nil, response:)
      rescue Faraday::Error => e
        handle_error(request_body: nil, exception: e)
      end

      def get_transactions(loanpro_loan_id)
        response = connection.get("odata.svc/Loans(#{loanpro_loan_id})/Transactions?nopaging=true")
        handle_success(request_body: nil, response:)
      rescue Faraday::Error => e
        handle_error(request_body: nil, exception: e)
      end

      def get_loan(loanpro_loan_id, expand: nil)
        query_params = expand.present? ? "$expand=#{expand}&nopaging=true" : 'nopaging=true'
        response = connection.get("odata.svc/Loans(#{loanpro_loan_id})?#{query_params}")
        handle_success(request_body: nil, response:)
      rescue Faraday::Error => e
        handle_error(request_body: nil, exception: e)
      end

      def get_autopay(autopay_id, expand: nil)
        query_params = expand.present? ? "$expand=#{expand}&nopaging=true" : 'nopaging=true'
        response = connection.get("odata.svc/Autopays(#{autopay_id})?#{query_params}")
        handle_success(request_body: nil, response:)
      rescue Faraday::Error => e
        handle_error(request_body: nil, exception: e)
      end

      def cancel_autopay(loanpro_loan_id, autopay_id)
        meta = { loanpro_loan_id:, autopay_id: }
        request_body = {
          Autopays: {
            results: [
              {
                id: autopay_id,
                status: 'autopay.status.cancelled',
                __update: true
              }
            ]
          }
        }
        response = connection.put("odata.svc/Loans(#{loanpro_loan_id})", request_body.to_json)

        handle_success(request_body:, response:, meta:)
      rescue ::StandardError => e
        handle_error(request_body:, exception: e, meta:)
      end

      # result returns
      # results [array] of loanpro loans
      # summary [hash] of start, pageSize, and total returned

      # Queries the LoanPro API for active loans matching the given unified_id.
      # Returns a hash with:
      # - results [Array<Hash>] — LoanPro loan objects
      # - summary [Hash] — includes start, pageSize, and total
      def get_active_loanpro_loan_by(unified_id:)
        query_params = "$filter=displayId eq '#{unified_id}' and temporaryAccount eq 0 " \
                       'and active eq 1 and archived eq 0'
        meta = { unified_id:, query_params: }

        response = connection.get("odata.svc/Loans()?#{query_params}")

        handle_success(request_body: nil, response:, meta:)
      rescue ::StandardError => e
        handle_error(request_body: nil, exception: e, meta:)
      end

      # search_active_loanpro_loan_by(unified_id:)
      #
      # Calls the LoanPro API search api for active loans associated with the given `unified_id`,
      # and returns active loans or Paid Off loans.  Using the search api, allows us to query the
      # StatusArchive entity in one query, which we can not do through the Loans() endpoint.
      #
      # Search API: https://developers.loanpro.io/reference/search-loans
      #
      # Returns a hash with:
      # - :results [Array<Hash>] — List of LoanPro loan objects
      # - :summary [Hash] — Metadata including :start, :totalHits, and :total
      def search_active_loanpro_loan_by(unified_id:) # rubocop:disable Metrics/MethodLength
        request_body = {
          query: {
            bool: {
              must: [
                { match: { displayId: unified_id } },
                { term: { active: 1 } }
              ],
              should: [
                { term: { archived: 0 } },
                { term: { loanStatusId: PAID_OFF_LOAN_STATUS_ID } }
              ],
              minimum_should_match: 1
            }
          }
        }
        meta = { unified_id:, request_body: }

        response = connection.post('Loans/Autopal.Search()?$top=25&$start=0', request_body.to_json) do |req|
          req.options.timeout = SEARCH_TIMEOUT
        end

        handle_success(request_body: nil, response:, meta:)
      rescue Faraday::TimeoutError => e
        raise TimeoutError.new(e, e.response)
      rescue ::StandardError => e
        handle_error(request_body: nil, exception: e, meta:)
      end

      def generate_dynamic_template(loanpro_loan_id, template_id)
        request_body = { id: template_id.to_s }
        response = connection.post("custom.forms/export/PDF/Loan/#{loanpro_loan_id}", request_body.to_json)

        # The response body is complete different, so I could not use handle_success
        RecordApiEvent.call(event_name: 'loanpro_generate_dynamic_template', request_body:, response:, meta: {})
        nil
      rescue Faraday::Error => e
        handle_error(request_body:, exception: e)
      end

      def download_dynamic_template(loanpro_loan_id)
        filter = "entityType eq 'Entity.CustomForm_#{loanpro_loan_id}'"
        response = connection.get("odata.svc/DataDumps?$filter=#{filter}")
        handle_success(request_body: nil, response:)
      rescue Faraday::Error => e
        handle_error(request_body: nil, exception: e)
      end

      private

      def create_customer_body(new_customer) # rubocop:disable Metrics/MethodLength
        body = {
          status: 'Active',
          customerType: 'customer.type.individual',
          customerIdType: 'customer.idType.ssn',
          gender: 'customer.gender.unknown',
          generationCode: 'customer.generationCode.none',
          Phones: {
            results: [
              {
                # Loanpro throws duplicate warnings
                __ignoreWarnings: true,
                phone: new_customer.phone_number,
                isPrimary: '1',
                isSecondary: '0',
                delete: false,
                _index: 0,
                type: 'customer.phoneType.cell',
                __isDirty: false,
                carrierVerified: 1,
                __lookupInProgress: true,
                carrierName: '',
                isLandLine: 0
              }
            ]
          },
          PrimaryAddress: {
            # Loanpro throws duplicate warnings
            __ignoreWarnings: true,
            country: 'company.country.usa',
            address1: new_customer.street_address,
            zipcode: new_customer.zip_code,
            city: new_customer.city,
            state: new_customer.state,
            verify: true
          },
          MailAddress: {
            # Loanpro throws duplicate warnings
            __ignoreWarnings: true,
            country: 'company.country.usa',
            address1: new_customer.street_address,
            zipcode: new_customer.zip_code,
            city: new_customer.city,
            state: new_customer.state,
            verify: true
          },
          # Loanpro throws duplicate warnings
          __ignoreWarnings: true,
          birthDate: new_customer.date_of_birth,
          firstName: new_customer.first_name,
          lastName: new_customer.last_name,
          ssn: new_customer.ssn,
          email: new_customer.email,
          PaymentAccounts: {
            results: [
              {
                CheckingAccount: {
                  accountType: new_customer.bank_account_type,
                  token: new_customer.bank_account_token
                },
                active: 1,
                isPrimary: 1,
                isSecondary: 0,
                title: "Personal Account #{new_customer.unified_id}",
                type: 'paymentAccount.type.checking'
              }
            ]
          }
        }

        apply_ignore_warnings(body)
      end

      def connection
        Faraday.new(url: base_url, headers:) do |f|
          FaradayHelper.middleware(f)
        end
      end

      def handle_success(request_body:, response:, meta: {})
        # NOTE:  Pulls calling location from stack
        # https://www.rubydoc.info/stdlib/core/2.0.0/Kernel%3Acaller_locations
        event_name = "loanpro_#{caller_locations(1, 1).first.base_label}"
        RecordApiEvent.call(event_name:, request_body:, response:, meta:)
        response.body&.dig('d')
      end

      def handle_error(request_body:, exception:, meta: {})
        # NOTE:  Pulls calling location from stack
        # https://www.rubydoc.info/stdlib/core/2.0.0/Kernel%3Acaller_locations
        event_name = "loanpro_#{caller_locations(1, 1).first.base_label}"

        Rails.logger.error("#{self} - #{exception.class}", error_message: exception.message)
        Rails.logger.error("#{self} - Unsuccessful response", meta)

        RecordApiEvent.call(event_name:, request_body:, response: exception.response, meta:)
        raise Error.new(exception, exception.response)
      end
    end
  end
end
# rubocop:enable Metrics/ClassLength
