# frozen_string_literal: true

module Clients
  class DashApi
    class IlaInputs
      include ActiveModel::Model
      include ActiveModel::Attributes

      # This is a representation of the "TIL Data" that provides the necessary information to
      # execute the required set of CRB Onboarding/funding validation checks. It comes from
      # https://github.com/Above-Lending/crb-onboard/blob/d28580a64c2a846a3fc17d520fcc0cf35b977dc6/helpers/arixValidation.js#L204

      attribute :address, :string
      attribute :apr, :decimal
      attribute :cash_back_amount, :integer
      attribute :contract_date, :date
      attribute :first_name, :string
      attribute :last_name, :string
      attribute :interest_rate, :decimal
      attribute :loan_number, :string
      attribute :origination_fee_amount, :decimal
      attribute :payment_due_on, :date
      attribute :zip_code # Leaving data type off because tilData.borrower.zip_code is in a JSONB column

      def to_payload
        {
          'address' => address,
          'apr' => apr.to_f,
          'cash_back_amount' => cash_back_amount,
          'contract_date' => contract_date,
          'first_name' => first_name,
          'last_name' => last_name,
          'interest_rate' => interest_rate.to_f,
          'loan_number' => loan_number,
          'origination_fee_amount' => origination_fee_amount.to_f,
          'payment_due_on' => payment_due_on,
          'zip_code' => zip_code
        }
      end
    end
  end
end
