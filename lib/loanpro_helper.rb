# frozen_string_literal: true

# LoanPro's API is a bit cludgy. These helpers will make it easier to work with.
class LoanproHelper
  LOAN_SUB_STATUSES = {
    unknown: 'Unknown',
    funding: 'Funding',
    closed: 'Closed',
    good_standing: 'Good Standing',
    past_due: 'Past Due',
    charged_off: 'Closed - Charged Off',
    debt_sale: 'Closed - Debt Sale'
  }.freeze

  AUTO_PAY_STATUS = {
    completed: 'autopay.status.completed',
    cancelled: 'autopay.status.cancelled',
    failed: 'autopay.status.failed',
    pending: 'autopay.status.pending'
  }.freeze

  AUTO_PAY_TYPES = {
    single: 'autopay.type.single',
    recurring: 'autopay.type.recurring',
    recurring_match: 'autopay.type.recurringMatch'
  }.freeze

  PAYMENT_STATUS = {
    success: 'payment.status.success',
    failed: 'payment.status.failed',
    voided: 'payment.status.voided',
    reversed: 'payment.status.reversed',
    unknown: 'payment.status.unknown',
    none: 'payment.status.none'
  }.freeze

  # TODO: Migrate this to use beneficial owner records instead of a constant
  DEBT_SALE_BENEFICIAL_NAME = ['Titan Asset Purchasing', 'Velocity Investments, LLC'].freeze

  LOAN_CLOSED_CHARGED_OFF_SUB_STATUS = 36
  LOAN_CLOSED_DEBT_SALE = 60
  LOAN_FUNDING_STATUSES = [1].freeze
  LOAN_CLOSED_STATUSES = [52, 36, 54, 53, 16, 33, 39, LOAN_CLOSED_CHARGED_OFF_SUB_STATUS, LOAN_CLOSED_DEBT_SALE].freeze
  LOAN_ACTIVE_STATUSES = [51, 50, 9].freeze

  # LoanPro dates are serialized by default in an outdated Microsoft format (e.g. "/Date(1758326400)/"). This class
  # provides various utility methods to assist in parsing and working with dates in this format.
  def self.parse_date(ms_string_value)
    return nil if ms_string_value.blank?

    ms_string_value = ms_string_value.to_s
    return nil unless ms_string_value.start_with?('/Date(') && ms_string_value.end_with?(')/')

    Time.zone.at(ms_string_value[6..-2].to_i).to_date
  end

  def self.parse_float(value)
    return value if value.is_a?(Numeric)
    return nil if value.blank? || !value.is_a?(String)

    value.to_f
  end

  def self.parse_payment_schedule(loan_setup_data)
    return [] if loan_setup_data.blank? || loan_setup_data['tilPaymentSchedule'].blank?

    JSON.parse(loan_setup_data['tilPaymentSchedule'])
  end

  def self.loan_sub_status(sub_status_id, days_past_due = 0)
    case sub_status_id
    when *LOAN_FUNDING_STATUSES
      LOAN_SUB_STATUSES[:funding]
    when LOAN_CLOSED_CHARGED_OFF_SUB_STATUS
      LOAN_SUB_STATUSES[:charged_off]
    when *LOAN_CLOSED_STATUSES
      LOAN_SUB_STATUSES[:closed]
    when *LOAN_ACTIVE_STATUSES
      days_past_due&.positive? ? LOAN_SUB_STATUSES[:past_due] : LOAN_SUB_STATUSES[:good_standing]
    else
      LOAN_SUB_STATUSES[:unknown]
    end
  end
end
