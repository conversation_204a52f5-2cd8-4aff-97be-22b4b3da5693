name: Ruby

on: [push]
defaults:
  run:
    shell: bash

env:
  BUNDLE_GITHUB__COM: ${{ secrets.GH_TOKEN }}:x-oauth-basic
  BUNDLE_GEMS__CONTRIBSYS__COM: ${{ secrets.BUNDLE_GEMS__CONTRIBSYS__COM }}
  RAILS_ENV: test
  PGHOST: localhost
  PGPASSWORD: postgres
  PGUSER: postgres

jobs:
  rubocop:
    name:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4
      # cache-apt-pkgs-action needs apt-fast, which needs wget and aria2
      - name: Prepare runner to use apt cache
        run: sudo apt-get -yqq update && sudo apt-get -yqq install wget aria2
      - name: Install Dependencies
        uses: awalsh128/cache-apt-pkgs-action@v1.4.3
        with:
          packages: libpq-dev ubuntu-dev-tools
          version: 1.0
      - name: Install Ruby and gems
        uses: ruby/setup-ruby@v1
        with:
          bundler-cache: true
      - name: Run Rubocop
        run: bundle exec rubocop --parallel
  brakeman:
    name:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      # cache-apt-pkgs-action needs apt-fast, which needs wget and aria2
      - name: Prepare runner to use apt cache
        run: sudo apt-get -yqq update && sudo apt-get -yqq install wget aria2
      - name: Install Dependencies
        uses: awalsh128/cache-apt-pkgs-action@v1.4.3
        with:
          packages: libpq-dev ubuntu-dev-tools
          version: 1.0
      - name: Install Ruby and gems
        uses: ruby/setup-ruby@v1
        with:
          bundler-cache: true
      - name: Run brakeman
        run: bundle exec brakeman
  erb_lint:
    name:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4
      # cache-apt-pkgs-action needs apt-fast, which needs wget and aria2
      - name: Prepare runner to use apt cache
        run: sudo apt-get -yqq update && sudo apt-get -yqq install wget aria2
      - name: Install Dependencies
        uses: awalsh128/cache-apt-pkgs-action@v1.4.3
        with:
          packages: libpq-dev ubuntu-dev-tools
          version: 1.0
      - name: Install Ruby and gems
        uses: ruby/setup-ruby@v1
        with:
          bundler-cache: true
      - name: Run ERBLint
        run: bundle exec erb_lint .
  test:
    name:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15.10
        credentials:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
        env:
          POSTGRES_DB: abovelending_test
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      redis:
        image: redis
        credentials:
            username: ${{ secrets.DOCKERHUB_USERNAME }}
            password: ${{ secrets.DOCKERHUB_TOKEN }}
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      # cache-apt-pkgs-action needs apt-fast, which needs wget and aria2
      - name: Prepare runner to use apt cache
        run: sudo apt-get -yqq update && sudo apt-get -yqq install wget aria2
      - name: Install Dependencies
        uses: awalsh128/cache-apt-pkgs-action@v1.4.3
        with:
          packages: >-
            libpq-dev
            ubuntu-dev-tools
            postgresql-client
            default-mysql-client
            libmysqlclient-dev
            libglib2.0-0
            libgconf-2-4
            libatk1.0-0
            libatk-bridge2.0-0
            libgdk-pixbuf2.0-0
            libgtk-3-0
            libgbm-dev
            libnss3-dev
            libxss-dev
            libasound2
            xvfb
            fonts-liberation
            libu2f-udev
            xdg-utils
          version: 1.0
      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          bundler-cache: true # runs 'bundle install' and caches installed gems automatically
      - name: Database setup
        run: bin/parallel_test -e 'bin/rails db:test:prepare'

      - name: Cache node_modulees
        uses: actions/cache@v3
        with:
          path: |
            node_modules
          key: node_modules-${{ hashFiles('yarn.lock') }}
      - uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'
          cache: yarn
      - name: 'Install Node packages'
        run: yarn install --immutable --prefer-offline
      - name: Compile assets
        run: bin/rails assets:precompile

      - name: 'Restore test runtimes'
        id: cache-restore-runtimes
        uses: actions/cache/restore@v4
        with:
          key: rspec-runtime-cache-${{ github.run_id }}
          restore-keys: |
            rspec-runtime-cache-${{ github.run_id }}
            rspec-runtime-cache-
          path: tmp/parallel_runtime_rspec.log
      - name: Run RSpec
        run: |
          mkdir /tmp/test-results
          bin/parallel_rspec
      - name: 'Save unit tests runtimes'
        uses: actions/cache/save@v4
        id: cache-save-runtimes
        with:
          key:  rspec-runtime-cache-${{ github.run_id }}
          path: tmp/parallel_runtime_rspec.log
      - name: Publish JUnit Test Report
        uses: mikepenz/action-junit-report@v4
        if: ${{ !cancelled() }}
        with:
          report_paths: '/tmp/test-results/rspec*.xml'
      - name: Prepare file name for coverage
        if: failure()
        id: coverage_file_name
        run: echo "value=$(echo ${{ github.head_ref || github.ref_name }} | sed "s/[^[:alnum:].-]/_/g")" >> $GITHUB_OUTPUT
      - name: Upload coverage
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: ${{ steps.coverage_file_name.outputs.value }}-coverage
          path: coverage
          retention-days: 1
          overwrite: true
